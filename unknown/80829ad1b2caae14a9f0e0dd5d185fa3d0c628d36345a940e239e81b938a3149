/*eslint-disable*/
import { useRef, useCallback } from 'react';
import { Track, RoomEvent } from 'livekit-client';
import { generateAvatar } from '../../../utils/helper';

// Utility functions for grid calculations
const getRowCount = (length) => {
  return length > 2 ? 2 : length > 0 ? 1 : 0;
};

const getColCount = (length) => {
  return length < 2 ? 1 : length < 5 ? 2 : 3;
};

// Design configuration for PiP
const pipDesign = {
  header: {
    height: 48, // Increased header height
    backgroundColor: "#1a1a1a",
    textColor: "#ffffff",
    fontSize: 18, // Increased font size
    showControls: false, // Disabled to remove buttons
    controlsColor: "#ffffff",
    controlsSize: 24, // Increased control size
  },
  background: {
    type: "solid",
    color: "#2c2c2c",
  },
  border: {
    width: 2,
    color: "#3a3a3a",
    radius: 8,
  },
  logo: {
    show: false,
    position: "bottomRight",
    text: "<PERSON><PERSON><PERSON>",
    fontSize: 16, // Increased font size
    fontColor: "rgba(255, 255, 255, 0.6)",
  },
  videoSpacing: 6, // Increased spacing
  contentPadding: 12, // Increased padding
  speakingDelay: 1000, // Delay in ms before showing a new speaker
  maxSmallWindows: 2, // Maximum number of small windows to show
};

export function useSaasPictureInPicture({
  setIsPIPEnabled,
  room,
  setToastNotification,
  setToastStatus,
  setShowToast,
  isElectronApp
}) {
  const pipWindowRef = useRef(null);
  const togglePipMode = useCallback(async (enabled) => {
    // Check browser compatibility
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isFirefox = navigator.userAgent.toLowerCase().indexOf("firefox") > -1;

    if (isSafari || isFirefox) {
      setToastNotification(
        "Picture-in-Picture is not supported in your browser"
      );
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    if (pipWindowRef.current && document.pictureInPictureElement) {
      await document.exitPictureInPicture();
      pipWindowRef.current = null;
      return;
    }
    if (!enabled) return;
    if ("pictureInPictureEnabled" in document) {
      const source = document.createElement("canvas");
      // Increase canvas size for better visibility
      source.width = 640; // Increased from 1920
      source.height = 360; // Increased from 1080
      const ctx = source.getContext("2d", { alpha: false });
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      const pipVideo = document.createElement("video");
      pipWindowRef.current = pipVideo;
      pipVideo.autoplay = true;
      pipVideo.playsInline = true;
      // Set video size to be larger
      pipVideo.style.width = "640px"; // Set explicit width
      pipVideo.style.height = "360px"; // Set explicit height
      pipVideo.style.minWidth = "640px"; // Ensure minimum size
      pipVideo.style.minHeight = "360px";

      const stream = source.captureStream(60);
      pipVideo.srcObject = stream;
      drawCanvas();

      // Add Media Session handlers
      if ("mediaSession" in navigator) {
        // Ensure tracks are published
        const ensureTracksPublished = async () => {
          // Ensure camera track is published
          if (!room.localParticipant.getTrackPublication(Track.Source.Camera)) {
            await room.localParticipant.setCameraEnabled(true);
            await room.localParticipant.setCameraEnabled(false);
          }

          // Ensure microphone track is published
          if (
            !room.localParticipant.getTrackPublication(Track.Source.Microphone)
          ) {
            await room.localParticipant.setMicrophoneEnabled(true);
            await room.localParticipant.setMicrophoneEnabled(false);
          }
        };

        // Function to update media session metadata
        const updateMediaSessionMetadata = () => {
          const cameraTrack = room.localParticipant.getTrackPublication(
            Track.Source.Camera
          );
          const micTrack = room.localParticipant.getTrackPublication(
            Track.Source.Microphone
          );
          const isCameraEnabled = cameraTrack ? !cameraTrack.isMuted : false;
          const isMicEnabled = micTrack ? !micTrack.isMuted : false;

          navigator.mediaSession.setCameraActive(isCameraEnabled);
          navigator.mediaSession.setMicrophoneActive(isMicEnabled);

          navigator.mediaSession.metadata = new MediaMetadata({
            title: "Daakia",
            artist: "Video Conference ",
            artwork: [
              {
                src: `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 96 96">
                  <rect width="96" height="96" fill="${
                    isMicEnabled ? "#00ff00" : "#ff0000"
                  }"/>
                  <text x="48" y="48" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dominant-baseline="middle">
                    ${isMicEnabled ? "Mic On" : "Mic Off"}
                  </text>
                </svg>`,
                sizes: "96x96",
                type: "image/svg+xml",
              },
            ],
          });
        };

        // Initialize tracks before setting up handlers
        ensureTracksPublished().then(() => {
          // Set initial states
          updateMediaSessionMetadata();

          // Add track state change listeners
          room.on(RoomEvent.TrackMuted, updateMediaSessionMetadata);
          room.on(RoomEvent.TrackUnmuted, updateMediaSessionMetadata);

          // Add action handlers
          navigator.mediaSession.setActionHandler("hangup", () => {
            if (isElectronApp) {
              window?.electronAPI?.ipcRenderer?.send("stop-annotation");
            }
            room.disconnect();
          });

          // Initialize camera handler with current state
          navigator.mediaSession.setActionHandler("togglecamera", () => {
            const cameraTrack = room.localParticipant.getTrackPublication(
              Track.Source.Camera
            );
            if (cameraTrack) {
              const isCurrentlyEnabled = !cameraTrack.isMuted;
              room.localParticipant.setCameraEnabled(!isCurrentlyEnabled);
            }
          });

          // Initialize microphone handler with current state
          navigator.mediaSession.setActionHandler("togglemicrophone", () => {
            const micTrack = room.localParticipant.getTrackPublication(
              Track.Source.Microphone
            );
            if (micTrack) {
              const isCurrentlyEnabled = !micTrack.isMuted;
              room.localParticipant.setMicrophoneEnabled(!isCurrentlyEnabled);
            }
          });
        });

        // Clean up track state change listeners when leaving PiP
        pipVideo.addEventListener("leavepictureinpicture", (event) => {
          pipWindowRef.current = null;
          setIsPIPEnabled(false);
          pipVideo.srcObject.getTracks().forEach((track) => track.stop());

          // Clean up media session handlers and track listeners
          if ("mediaSession" in navigator) {
            navigator.mediaSession.setActionHandler("hangup", null);
            navigator.mediaSession.setActionHandler("togglecamera", null);
            navigator.mediaSession.setActionHandler("togglemicrophone", null);
            room.off(RoomEvent.TrackMuted, updateMediaSessionMetadata);
            room.off(RoomEvent.TrackUnmuted, updateMediaSessionMetadata);
          }
        });
      }

      pipVideo.onloadedmetadata = () => {
        pipVideo.requestPictureInPicture();
      };
      await pipVideo.play();

      pipVideo.addEventListener("enterpictureinpicture", (event) => {
        drawCanvas();
        setIsPIPEnabled(true);
      });

      pipVideo.addEventListener("leavepictureinpicture", (event) => {
        pipWindowRef.current = null;
        setIsPIPEnabled(false);
        pipVideo.srcObject.getTracks().forEach((track) => track.stop());

        // Clean up media session handlers when leaving PiP
        if ("mediaSession" in navigator) {
          navigator.mediaSession.setActionHandler("hangup", null);
          navigator.mediaSession.setActionHandler("togglecamera", null);
          navigator.mediaSession.setActionHandler("togglemicrophone", null);
        }
      });

      function drawCanvas() {
        const videos = document.querySelectorAll("video");
        try {
          // Only log if there are videos and they have changed
          if (videos.length > 0) {
            const currentVideoState = Array.from(videos).map((video) => {
              const tracks = video.srcObject?.getVideoTracks() || [];
              return {
                trackLabel: tracks[0]?.label,
                trackKind: tracks[0]?.kind,
                trackSettings: tracks[0]?.getSettings(),
                trackConstraints: tracks[0]?.getConstraints(),
                trackEnabled: tracks[0]?.enabled,
                trackMuted: tracks[0]?.muted,
                trackReadyState: tracks[0]?.readyState,
                parentElement: video.parentElement?.className,
                videoElement: {
                  id: video.id,
                  className: video.className,
                  width: video.videoWidth,
                  height: video.videoHeight,
                },
              };
            });

            // Compare with previous state
            const stateChanged =
              JSON.stringify(currentVideoState) !==
              JSON.stringify(window._lastVideoState);
            if (stateChanged) {
              window._lastVideoState = currentVideoState;
            }
          }

          drawCustomBackground(ctx, source.width, source.height);

          // Find screen share video
          const screenShareVideo = Array.from(videos).find((video) => {
            const tracks = video.srcObject?.getVideoTracks() || [];
            const track = tracks[0];
            return (
              track?.label?.startsWith("screen:") ||
              track?.label?.startsWith("window:") ||
              track?.label?.startsWith("web-contents-media-stream://")
            );
          });

          // Calculate content area (excluding header)
          const contentHeight = source.height - pipDesign.header.height;
          const contentY = pipDesign.header.height;

          if (screenShareVideo && screenShareVideo.readyState === 4) {
            // If screen share exists, show only that in full screen but with design elements
            const videoAspect =
              screenShareVideo.videoWidth / screenShareVideo.videoHeight;
            const canvasAspect = source.width / contentHeight;

            let drawWidth,
              drawHeight,
              offsetX = 0,
              offsetY = contentY;

            if (videoAspect > canvasAspect) {
              drawWidth = source.width - pipDesign.border.width * 2;
              drawHeight = drawWidth / videoAspect;
              offsetY = contentY + (contentHeight - drawHeight) / 2;
            } else {
              drawHeight = contentHeight - pipDesign.border.width * 2;
              drawWidth = drawHeight * videoAspect;
              offsetX = (source.width - drawWidth) / 2;
            }

            try {
              // Draw rounded rectangle for screen share
              drawRoundedRect(
                ctx,
                offsetX,
                offsetY,
                drawWidth,
                drawHeight,
                pipDesign.border.radius
              );

              // Draw the screen share video
              ctx.save();
              ctx.clip();
              ctx.drawImage(
                screenShareVideo,
                offsetX,
                offsetY,
                drawWidth,
                drawHeight
              );
              ctx.restore();

              // Draw border
              ctx.strokeStyle = pipDesign.border.color;
              ctx.lineWidth = pipDesign.border.width;
              ctx.stroke();

              // Check if camera is enabled
              const cameraTrack = room.localParticipant.getTrackPublication(
                Track.Source.Camera
              );
              const isCameraEnabled = cameraTrack
                ? !cameraTrack.isMuted
                : false;

              // Check if microphone is enabled
              const micTrack = room.localParticipant.getTrackPublication(
                Track.Source.Microphone
              );
              const isMicEnabled = micTrack ? !micTrack.isMuted : false;

              // Get speaking state from participant
              const isSpeaking = room.localParticipant.isSpeaking;

              // Get local participant's video track
              const localVideo = Array.from(videos).find((video) => {
                const tracks = video.srcObject?.getVideoTracks() || [];
                const track = tracks[0];
                return (
                  track?.label?.includes("camera") ||
                  track?.label?.includes("Camera")
                );
              });

              // Get all participants (local and remote)
              const allParticipants = [
                room.localParticipant,
                ...Array.from(room.remoteParticipants.values()),
              ];

              // Track speaking participants with timestamps
              const speakingParticipants = allParticipants.filter(
                (participant) => {
                  const isSpeaking = participant.isSpeaking;
                  const micTrack = participant.getTrackPublication(
                    Track.Source.Microphone
                  );
                  const isMicEnabled = micTrack ? !micTrack.isMuted : false;
                  return isSpeaking && isMicEnabled;
                }
              );

              // Sort by most recent speaking (if we have timestamps)
              speakingParticipants.sort((a, b) => {
                const aTime = a.lastSpeakingTime || 0;
                const bTime = b.lastSpeakingTime || 0;
                return bTime - aTime;
              });

              // Take only the most recent speakers up to maxSmallWindows
              const recentSpeakers = speakingParticipants.slice(
                0,
                pipDesign.maxSmallWindows
              );

              // Calculate small window dimensions
              const smallWindowWidth = source.width * 0.25;
              const smallWindowHeight = source.height * 0.25;
              const smallWindowSpacing = 10;

              // Show small windows for speaking participants
              recentSpeakers.forEach((participant, index) => {
                const smallWindowX = source.width - smallWindowWidth - 20;
                const smallWindowY =
                  source.height -
                  (smallWindowHeight + smallWindowSpacing) * (index + 1) -
                  20;

                // Draw rounded rectangle for small window
                drawRoundedRect(
                  ctx,
                  smallWindowX,
                  smallWindowY,
                  smallWindowWidth,
                  smallWindowHeight,
                  pipDesign.border.radius
                );

                // Draw animated border if speaking
                const time = Date.now() / 1000;
                const pulseIntensity = (Math.sin(time * 3) + 1) / 2;
                const borderWidth = 2 + pulseIntensity * 2;
                const borderColor = `rgba(30, 140, 250, ${
                  0.5 + pulseIntensity * 0.5
                })`;

                ctx.strokeStyle = borderColor;
                ctx.lineWidth = borderWidth;
                ctx.stroke();

                // Draw avatar with first letter
                ctx.save();
                ctx.clip();

                // Draw background circle
                const centerX = smallWindowX + smallWindowWidth / 2;
                const centerY = smallWindowY + smallWindowHeight / 2;
                const radius =
                  Math.min(smallWindowWidth, smallWindowHeight) / 2 - 10;

                // Animate circle size based on speaking state
                const pulseScale = 1 + Math.sin(Date.now() / 200) * 0.1;
                const scaledRadius = radius * pulseScale;

                ctx.beginPath();
                ctx.arc(centerX, centerY, scaledRadius, 0, Math.PI * 2);
                ctx.fillStyle = "#1a1a1a";
                ctx.fill();

                // Draw first letter of name
                const name = participant.name || "U";
                const avatarText = generateAvatar(name);
                // Adjust font size based on number of characters
                const fontSize = avatarText.length > 1 ? radius * 0.8 : radius;
                ctx.font = `bold ${fontSize}px Arial`;
                ctx.fillStyle = "#ffffff";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(avatarText, centerX, centerY);

                // Add small indicator for local participant
                if (participant.identity === room.localParticipant.identity) {
                  ctx.font = "12px Arial";
                  ctx.fillStyle = "rgba(255, 255, 255, 0.7)";
                  ctx.fillText("You", centerX, centerY + radius + 15);
                }

                ctx.restore();
              });
            } catch (error) {
              console.error("Error in drawCanvas:", error);
              console.warn("Error drawing screen share:", error);
            }
          } else {
            // Show single window that switches between local and remote
            const cellWidth = source.width;
            const cellHeight = contentHeight;

            // Get all remote participants with their states
            const remoteParticipants = Array.from(
              room.remoteParticipants.values()
            );

            // Find participants who are speaking
            const speakingParticipants = remoteParticipants.filter(
              (p) => p.isSpeaking
            );

            // Priority order for showing participants:
            // 1. Speaking remote participant
            // 2. Local participant
            let participantToShow = room.localParticipant;

            if (speakingParticipants.length > 0) {
              // If multiple people are speaking, show the most recent speaker
              participantToShow = speakingParticipants.sort(
                (a, b) => (b.lastSpeakingTime || 0) - (a.lastSpeakingTime || 0)
              )[0];
            }

            const isLocalParticipant =
              participantToShow.identity === room.localParticipant.identity;

            // Find the video for the participant to show (only for local participant)
            const videoToShow = isLocalParticipant
              ? Array.from(videos).find((video) => {
                  const tracks = video.srcObject?.getVideoTracks() || [];
                  return tracks.some(
                    (track) =>
                      track.label?.includes("camera") ||
                      track.label?.includes("Camera")
                  );
                })
              : null;

            // Draw window
            drawRoundedRect(
              ctx,
              0,
              contentY,
              cellWidth,
              cellHeight,
              pipDesign.border.radius
            );
            ctx.save();
            ctx.clip();

            // Only show video for local participant, always show avatar for remote
            const videoTrack = isLocalParticipant
              ? participantToShow.getTrackPublication(Track.Source.Camera)
              : null;
            const isVideoEnabled =
              isLocalParticipant &&
              videoTrack &&
              !videoTrack.isMuted &&
              videoToShow &&
              videoToShow.readyState === 4;

            if (isVideoEnabled) {
              // Draw video if enabled (only for local participant)
              ctx.drawImage(videoToShow, 0, contentY, cellWidth, cellHeight);
            } else {
              // Draw avatar
              const centerX = cellWidth / 2;
              const centerY = contentY + cellHeight / 2;
              const radius = Math.min(cellWidth, cellHeight) / 3;

              // Draw background circle with glow if speaking
              ctx.beginPath();
              ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);

              // Add glow effect if speaking
              if (participantToShow.isSpeaking) {
                const time = Date.now() / 1000;
                const pulseIntensity = (Math.sin(time * 3) + 1) / 2;
                const glowRadius = radius + pulseIntensity * 10; // Glow extends 10px max

                // Draw glow
                ctx.beginPath();
                ctx.arc(centerX, centerY, glowRadius, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(30, 140, 250, ${
                  0.2 + pulseIntensity * 0.3
                })`;
                ctx.fill();

                // Draw main circle
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
              }

              ctx.fillStyle = "#1a1a1a";
              ctx.fill();

              // Draw avatar text
              const name = participantToShow.name || "U";
              const avatarText = generateAvatar(name);
              const fontSize = avatarText.length > 1 ? radius * 0.8 : radius;
              ctx.font = `bold ${fontSize}px Arial`;
              ctx.fillStyle = "#ffffff";
              ctx.textAlign = "center";
              ctx.textBaseline = "middle";
              ctx.fillText(avatarText, centerX, centerY);
            }
            ctx.restore();

            // Draw border with glow effect if speaking
            if (participantToShow.isSpeaking) {
              // Animate border glow
              const time = Date.now() / 1000;
              const pulseIntensity = (Math.sin(time * 3) + 1) / 2;
              const borderWidth = 2 + pulseIntensity * 2;
              const borderColor = `rgba(30, 140, 250, ${
                0.5 + pulseIntensity * 0.5
              })`;

              ctx.strokeStyle = borderColor;
              ctx.lineWidth = borderWidth;
            } else {
              ctx.strokeStyle = pipDesign.border.color;
              ctx.lineWidth = pipDesign.border.width;
            }
            ctx.stroke();
          }
        } catch (error) {
          setToastNotification("Error toggling PIP mode.");
          setToastStatus("error");
          setShowToast(true);
          // console.log("Error toggling PIP mode.", error);
        }

        if (document.pictureInPictureElement === pipVideo) {
          requestAnimationFrame(drawCanvas);
        }
      }

      // Helper function to draw rounded rectangles
      function drawRoundedRect(ctx, x, y, width, height, radius) {
        if (width < 2 * radius) radius = width / 2;
        if (height < 2 * radius) radius = height / 2;

        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(
          x + width,
          y + height,
          x + width - radius,
          y + height
        );
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
      }

      // Helper function to draw custom background
      function drawCustomBackground(ctx, width, height) {
        // Draw main background
        ctx.fillStyle = pipDesign.background.color;
        ctx.fillRect(0, 0, width, height);

        // Draw header
        ctx.fillStyle = pipDesign.header.backgroundColor;
        ctx.fillRect(0, 0, width, pipDesign.header.height);

        // Draw header text
        ctx.font = `${pipDesign.header.fontSize}px Arial, sans-serif`;
        ctx.fillStyle = pipDesign.header.textColor;
        ctx.textAlign = "left";
        ctx.textBaseline = "middle";
        ctx.fillText("Daakia", 12, pipDesign.header.height / 2);
      }

      // Helper function to draw logo or text
      function drawLogoOrText(ctx, width, height) {
        ctx.save();

        // Set font properties
        ctx.font = `${pipDesign.logo.fontSize}px Arial, sans-serif`;
        ctx.fillStyle = pipDesign.logo.fontColor;
        ctx.textBaseline = "middle";

        const text = pipDesign.logo.text;
        const textWidth = ctx.measureText(text).width;
        const textHeight = pipDesign.logo.fontSize;
        const padding = 8;

        // Calculate position based on setting
        let x, y;
        switch (pipDesign.logo.position) {
          case "topLeft":
            x = padding;
            y = pipDesign.header.height + padding + textHeight / 2;
            ctx.textAlign = "left";
            break;
          case "topRight":
            x = width - padding;
            y = pipDesign.header.height + padding + textHeight / 2;
            ctx.textAlign = "right";
            break;
          case "bottomLeft":
            x = padding;
            y = height - padding - textHeight / 2;
            ctx.textAlign = "left";
            break;
          case "bottomRight":
          default:
            x = width - padding;
            y = height - padding - textHeight / 2;
            ctx.textAlign = "right";
            break;
        }

        // Draw text with subtle shadow
        ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;

        ctx.fillText(text, x, y);

        ctx.restore();
      }
    } else {
      setToastNotification(
        "Picture-in-Picture is not supported by your browser"
      );
      setToastStatus("error");
      setShowToast(true);
    }
  }, [room, setIsPIPEnabled, setToastNotification, setToastStatus, setShowToast, isElectronApp]);

  return {
    togglePipMode,
    isSupported: "pictureInPictureEnabled" in document
  };
}

