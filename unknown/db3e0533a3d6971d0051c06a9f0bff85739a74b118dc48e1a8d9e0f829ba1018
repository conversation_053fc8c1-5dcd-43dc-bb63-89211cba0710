// Import color variables
$primary-blue: #3B60E4;
$text-dark: #333;
$text-muted: #666;
$background-light: #f8f9fa;
$background-white: #fff;
$border-color: #e9ecef;
$sidebar-active-bg: #E6EBF6;
$success-green: #28a745;
$warning-yellow: #ffc107;
$danger-red: #dc3545;
$font: "Inter", sans-serif;

// Custom fieldset and input styling
.custom-fieldset {
  border: 1.5px solid #bdbdbd;
  border-radius: 8px;
  margin: 0;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 12px;
  background: #fff;
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
  
  &.error {
    border-color: #e53935;
  }
  
  &:focus-within {
    border-color: $primary-blue;
    box-shadow: 0 0 0 3px rgba(59, 96, 228, 0.1);
  }
  
  legend {
    width: auto;
    padding: 0 4px;
    margin-left: 12px;
  }
}

.custom-legend {
  font-size: 15px;
  color: #666;
  font-family: inherit;
  padding: 0 4px;
  margin-left: 12px;
  height: 20px;
  line-height: 20px;
  display: flex;
  align-items: center;
  background: #fff;
  /* Embed the legend inside the border */
  position: absolute;
  top: -12px;
  left: 16px;
  z-index: 1;
  pointer-events: none;
}

.custom-input {
  font-family: $font;
  flex: 1;
  padding: 13px;
  border: none;
  outline: none;
  width: 100%;
  font-size: 15px;
  background: transparent;
  margin-left: 0;
  font-family: inherit;
  color: #848484;
  min-height: 54px;
  min-width: 0;
  box-sizing: border-box;
  word-break: break-word;
  white-space: pre-line;
  cursor: text;
  
  &.error {
    color: #e53935;
  }
  
  &.color-input {
    width: 100px;
    padding: 6px 8px;
    font-size: 12px;
    font-family: monospace;
    text-align: center;
    min-height: auto;
    height: auto;
  }
  
  &::placeholder {
    color: #999;
  }
  
  &:focus {
    outline: none;
    border: none;
    box-shadow: none;
  }
}

.required {
  color: #e53935;
  margin-left: 2px;
  font-size: 15px;
  position: relative;
  top: -2px;
}

.error-message {
  font-family: $font;
  color: #e53935;
  font-size: 14px;
  margin-top: 4px;
  font-weight: 400;
  margin-left: 0;
  margin-right: 10px;
}

// Color Picker Styles
.color-picker-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 200px;
  margin: 0 auto;
  
  .color-label {
    color: $text-dark;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
  }
  
  .color-picker-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    
    .color-swatch {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      border: 1px solid $border-color;
      cursor: pointer;
      flex-shrink: 0;
    }
    
    .color-input {
      width: 100px;
      
      &.ant-input {
        padding: 6px 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 12px;
        font-family: monospace;
        text-align: center;
        
        &:focus,
        &:hover {
          border-color: $primary-blue;
        }
      }
    }
  }
}

// Upload Area Styles
.upload-area {
  border: 2px dashed $border-color !important;
  border-radius: 12px !important;
  padding: 8px 6px !important;
  text-align: center;
  background: $background-light !important;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 60px;
  max-width: 200px;
  width: 200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  &:hover {
    border-color: $primary-blue !important;
    background: rgba(59, 96, 228, 0.02) !important;
  }
  
  .upload-icon {
    font-size: 16px;
    margin-bottom: 4px;
    display: block;
    color: $text-muted;
    
    img {
      width: 24px;
      height: 17px;
      display: block;
      margin: 0 auto;
    }
  }
  
  .upload-text {
    color: $text-dark;
    font-size: 11px;
    margin: 0 0 2px 0;
    line-height: 1.2;
    
    .browse-link {
      color: $primary-blue;
      text-decoration: underline;
      cursor: pointer;
      
      &:hover {
        color: darken($primary-blue, 10%);
      }
    }
  }
  
  .upload-info {
    color: $text-muted;
    font-size: 9px;
    margin: 0;
    line-height: 1.2;
  }
}

// Toggle Settings Styles
.toggle-setting {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  
  .toggle-label {
    color: $text-dark;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    flex: 1;
  }
  
  .toggle-container {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .ant-switch {
      background: #ccc;
      
      &.ant-switch-checked {
        background: $primary-blue;
      }
    }
    
    .toggle-status {
      color: $text-muted;
      font-size: 12px;
      font-weight: 500;
      min-width: 60px;
      text-align: right;
    }
  }
}

// Coming Soon Button Styles
.coming-soon-button {
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
  color: #999 !important;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: not-allowed;
  display: block;
  margin: 0 auto;
  width: 200px;
  text-align: center;
  font-weight: 500;
  line-height: 1.5;
  height: auto;
  
  &:hover {
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
  }
  
  &:focus {
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
    box-shadow: none !important;
  }
}

// Mobile responsive adjustments
@media (max-width: 767px) {
  .custom-fieldset {
    min-height: 54px;
    max-height: 54px;
  }
  
  .custom-input {
    min-height: 54px;
    max-height: 54px;
    height: 54px;
    line-height: normal;
    
    &.color-input {
      min-height: auto;
      max-height: none;
      height: auto;
    }
  }
  
  .color-picker-container {
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 150px;
    
    .color-picker-wrapper {
      width: 100%;
      
      .color-input {
        flex: 1;
      }
    }
  }
  
  .upload-area {
    padding: 6px 4px !important;
    min-height: 50px;
    max-width: 150px;
    width: 150px;
    
    .upload-icon {
      font-size: 14px;
      margin-bottom: 3px;
      
      img {
        width: 20px;
        height: 14px;
      }
    }
    
    .upload-text {
      font-size: 10px;
    }
    
    .upload-info {
      font-size: 8px;
    }
  }
  
  .toggle-setting {
    padding: 12px 0;
    
    .toggle-label {
      font-size: 13px;
    }
    
    .toggle-container {
      .toggle-status {
        font-size: 11px;
      }
    }
  }
  
  .coming-soon-button {
    padding: 6px 12px;
    font-size: 13px;
    width: 150px;
    line-height: 1.4;
  }
} 