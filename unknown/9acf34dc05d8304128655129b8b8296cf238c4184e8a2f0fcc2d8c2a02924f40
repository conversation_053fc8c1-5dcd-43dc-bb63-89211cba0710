import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import { Button } from 'antd';
import { CustomInput } from '../../../CommonComponents';
import './CustomDomain.scss';

function CustomDomain() {
  const [domainName, setDomainName] = useState('');
  const [subDomainName, setSubDomainName] = useState('');

  return (
    <div className="custom-domain-wrapper">
      <div className="custom-domain">
        {/* Domain Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Domain</h3>
              <p className="setting-description">
                please write the host name
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="text"
                label="domain name"
                value={domainName}
                onChange={(e) => setDomainName(e.target.value)}
                placeholder="Input Text"
                required
              />
            </div>
          </Col>
        </Row>

        {/* Sub Domain Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Sub domain input</h3>
              <p className="setting-description">
                Write domain input e.g: meet.yourcompany.com
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="text"
                label="sub domain name"
                value={subDomainName}
                onChange={(e) => setSubDomainName(e.target.value)}
                placeholder="Input Text"
                required
              />
            </div>
          </Col>
        </Row>

        {/* Action Buttons */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="action-buttons">
              <Button type="primary" className="update-button">
                Update
              </Button>
              <Button className="cancel-button">
                Cancel
              </Button>
              <Button className="load-preview-button">
                Load Preview
              </Button>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default CustomDomain;