$primary-color: #1e1e1e;
$allow-button-color: #3b60e4;

.permissionsDrawer {
  position: relative;
  &Empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }
  :global(.sd-container-below) {
    padding-top: 1rem;
  }
  &Collapse {
    background-color: $primary-color;
    border: none;
    box-shadow: 0 0 10px 0 rgba(255, 255, 255, 0.2);
    :global(.ant-collapse-header) {
      color: #fff !important;
      display: flex;
      align-items: center !important;
      :global(.ant-collapse-expand-icon) {
        display: flex;
      }
    }
    :global(.ant-collapse-content) {
      background-color: $primary-color;
      box-shadow: 0 0 10px 0 rgba(255, 255, 255, 0.2);
      :global(.ant-collapse-content-box) {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  }
  &Item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &Name {
      color: white;
    }
    &Buttons {
      display: flex;
      justify-content: flex-end;
      gap: 5px;
      :global(.ant-btn) {
        border-radius: 0.5rem;
        cursor: pointer;
        &:nth-of-type(1) {
          background-color: $allow-button-color;
          color: #fff;
          border: none;
          &:hover {
            background-color: #7694ff;
          }
        }
        &:nth-of-type(2) {
          background-color: transparent;
          color: #fff;
          &:hover {
            border-color: #fff;
            background-color: #fff;
            color: #000;
          }
        }
      }
    }
  }
  &Title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

// Recording Consent Drawer
.consentList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  .consentItem {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .participantInfo {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .participantAvatar {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
        background-color: #fd4563;
      }

      .participantDetails {
        flex: 1;

        .participantName {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
        }

        .consentStatus {
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 6px;
          color: #b4b4b4;
          margin: 0;
          &:global(.pending) {
            color: #faad14;
          }

          .statusIcon {
            display: flex;
            align-items: center;
            justify-content: center;


            .iconBox {
              padding: 2px;
              border-radius: 1px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 10px;
              height: 10px;

              &.accepted {
                background-color: #52c41a;
              }

              &.denied {
                background-color: #ff4d4f;
              }

              &.pending {
                background-color: #faad14;
              }

              .anticon {
                font-size: 8px;
                color: #000;
                font-weight: 900;
                svg {
                  stroke-width: 100;
                  stroke: currentColor;
                }
              }
            }
          }

          &.accepted {
            color: #52c41a;
          }

          &.pending {
            color: #faad14;
          }

          &.denied {
            color: #ff4d4f;
          }
        }
      }
    }

    .refreshIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #3b60e4;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #7694ff;
      }

      .anticon {
        font-size: 16px;
        color: #ffffff;
      }
    }
  }
}
