import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import { Button, Radio } from 'antd';
import { CustomInput } from '../../../CommonComponents';
import './ColorTheme.scss';

function ColorTheme() {
  const [primaryColor, setPrimaryColor] = useState('#9E9E9E');
  const [secondaryColor, setSecondaryColor] = useState('#9E9E9E');
  const [accentColor, setAccentColor] = useState('#9E9E9E');
  const [selectedTheme, setSelectedTheme] = useState('light');

  const themes = [
    {
      id: 'light',
      name: 'Light theme',
      description: 'Clean and bright interface',
      preview: 'light-preview'
    },
    {
      id: 'dark',
      name: 'Dark theme',
      description: 'Easy on the eyes',
      preview: 'dark-preview'
    },
    {
      id: 'system',
      name: 'System theme',
      description: 'Follows your system preference',
      preview: 'system-preview'
    }
  ];

  const renderPreview = (theme) => {
    if (theme === 'system-preview') {
      return (
        <div className="preview-system">
          <div className="window-controls">
            <div className="control-btn red" />
            <div className="control-btn yellow" />
            <div className="control-btn green" />
          </div>
          <div className="system-content">
            {/* Left side boxes */}
            <div className="system-box left-large-1" />
            <div className="system-box left-small-1" />
            <div className="system-box left-small-2" />
            <div className="system-box left-large-2" />
            
            {/* Right side boxes */}
            <div className="system-box right-large-1" />
            <div className="system-box right-small-1" />
            <div className="system-box right-small-2" />
            <div className="system-box right-large-2" />
          </div>
        </div>
      );
    }

    return (
      <div className={`preview-${theme}`}>
        <div className="window-controls">
          <div className="control-btn red" />
          <div className="control-btn yellow" />
          <div className="control-btn green" />
        </div>
        <div className="content-placeholders">
          <div className="header-bar" />
          <div className="content-grid">
            <div className="large-box" />
            <div className="large-box" />
            <div className="small-box" />
            <div className="small-box" />
            <div className="small-box" />
            <div className="small-box" />
            <div className="large-box" />
            <div className="large-box" />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="color-theme-wrapper">
      <div className="color-theme">
        {/* Primary Color Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Primary color</h3>
              <p className="setting-description">
                The dominant color, typically used for backgrounds, large elements, and other main areas.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="color"
                label="Custom color"
                colorValue={primaryColor}
                onColorChange={(e) => setPrimaryColor(e.target.value)}
                placeholder="#9E9E9E"
              />
            </div>
          </Col>
        </Row>

        {/* Secondary Color Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Secondary color</h3>
              <p className="setting-description">
                Supports the primary color for elements like cards and carousels.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="color"
                label="Custom color"
                colorValue={secondaryColor}
                onColorChange={(e) => setSecondaryColor(e.target.value)}
                placeholder="#9E9E9E"
              />
            </div>
          </Col>
        </Row>

        {/* Accent Color Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Accent color</h3>
              <p className="setting-description">
                Highlights key elements like buttons and call to action.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="color"
                label="Custom color"
                colorValue={accentColor}
                onColorChange={(e) => setAccentColor(e.target.value)}
                placeholder="#9E9E9E"
              />
            </div>
          </Col>
        </Row>

        {/* Interface Theme Section */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="setting-info">
              <h3 className="setting-title">Interface theme</h3>
              <p className="setting-description">
                Select/Customize your workspace theme
              </p>
            </div>
          </Col>
        </Row>

        {/* Theme Selection Cards */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="theme-cards">
              {themes.map((theme) => (
                <div
                  key={theme.id}
                  className={`theme-card ${selectedTheme === theme.id ? 'active' : ''}`}
                  onClick={() => setSelectedTheme(theme.id)}
                >
                  <div className="theme-preview">
                    {renderPreview(theme.preview)}
                  </div>
                  <div className="theme-info">
                    <Radio checked={selectedTheme === theme.id} />
                    <span className="theme-name">{theme.name}</span>
                  </div>
                </div>
              ))}
            </div>
          </Col>
        </Row>

        {/* Action Buttons */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="action-buttons">
              <Button type="primary" className="update-button">
                Update
              </Button>
              <Button className="cancel-button">
                Cancel
              </Button>
              <Button className="load-preview-button">
                Load Preview
              </Button>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default ColorTheme; 