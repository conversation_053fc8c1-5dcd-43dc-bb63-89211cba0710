import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Collapse } from "antd";
import {
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import SideDrawer from "../SideDrawer";
import { DataReceivedEvent, DrawerState } from "../../utils/constants";
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";
import { ReactComponent as EmptyDrawerVector } from "./icons/EmptyDrawerVector.svg";
import styles from "../../styles/PermissionsDrawer.module.scss";
import { generateAvatar } from "../../utils/helper";

const { Panel } = Collapse;

export default function PermissionsDrawer({
  pendingScreenShareRequests,
  room,
  setPendingScreenShareRequests,
  participantConsent,
}) {
  const { openDrawer } = useVideoConferencesContext();

  // Screen Share Permission
  const onAllow = async (id) => {
    const encoder = new TextEncoder();
    const encodedMessage = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.REQUEST_SCREEN_SHARE_PERMISSION_RESPONSE,
        request_by: id,
        is_screen_share_allowed: true,
      })
    );
    await room.localParticipant.publishData(encodedMessage, {
      reliable: true,
      destinationIdentities: [id],
    });
    setPendingScreenShareRequests((prev) =>
      prev.filter((req) => req.userId !== id)
    );
  };
  const onDeny = async (id) => {
    const encoder = new TextEncoder();
    const encodedMessage = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.REQUEST_SCREEN_SHARE_PERMISSION_RESPONSE,
        request_by: id,
        is_screen_share_allowed: false,
      })
    );
    await room.localParticipant.publishData(encodedMessage, {
      reliable: true,
      destinationIdentities: [id],
    });
    setPendingScreenShareRequests((prev) =>
      prev.filter((req) => req.userId !== id)
    );
  };

  // Recording Consent
  // Add local state to track consent changes
  const [localConsent, setLocalConsent] = useState(participantConsent);

  // Update local state when participantConsent changes
  useEffect(() => {
    setLocalConsent(participantConsent);
  }, [participantConsent]);

  // Handler for refreshing participant consent
  const handleRefreshConsent = (participantId) => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.RECORDING_CONSENT_MODAL,
        value: true,
      })
    );
    room.localParticipant.publishData(data, {
      reliable: true,
      destinationIdentities: [participantId],
    });
  };

  const getStatusIcon = (status) => {
    if (status === "accept") {
      return (
        <span className="icon-box accepted">
          <CheckOutlined />
        </span>
      );
    }
    if (status === "reject") {
      return (
        <span className="icon-box denied">
          <CloseOutlined />
        </span>
      );
    }
    return (
      <span className={styles.statusIconPending}>
        !
      </span>
    );
  };

  const getStatusClass = (status) => {
    if (status === "accept") return "accepted";
    if (status === "reject") return "denied";
    return "pending";
  };

  const getStatusText = (status) => {
    if (status === "accept") return "Accepted";
    if (status === "reject") return "Denied";
    return "Pending";
  };

  return (
    <SideDrawer
      show={openDrawer === DrawerState.PERMISSIONS}
      // setShow={setIsRPDrawerOpen}
      title={
        <div className={styles.permissionsDrawerTitle}>
          <span>Permissions</span>
          <span className={styles.permissionsDrawerCount}>
            ({pendingScreenShareRequests.length + localConsent.length})
          </span>
        </div>
      }
      style={{ gap: "1rem" }}
      isWhiteboardOpen={false}
      className={styles.permissionsDrawer}
    >
      {(pendingScreenShareRequests.length > 0 || localConsent.length > 0) && (
        <Collapse
          defaultActiveKey={["1"]}
          accordion
          className={styles.permissionsDrawerCollapse}
        >
          {pendingScreenShareRequests.length > 0 && (
            <Panel
              header={
                <div className={styles.permissionsDrawerTitle}>
                  <span>Screen Share Requests</span>
                  <span className={styles.permissionsDrawerCount}>
                    ({pendingScreenShareRequests.length})
                  </span>
                </div>
              }
              key="1"
            >
              {pendingScreenShareRequests.map((req) => (
                <div key={req.userId} className={styles.permissionsDrawerItem}>
                  <div className={styles.permissionsDrawerItemName}>
                    {req.userName.split(" ")[0].length > 10
                      ? `${req.userName.split(" ")[0].substring(0, 10)}...`
                      : req.userName.split(" ")[0]}{" "}
                    {req.userName.split(" ").length > 1 &&
                      `${req.userName.split(" ")[1].charAt(0)}.`}
                  </div>
                  <div className={styles.permissionsDrawerItemButtons}>
                    <Button onClick={() => onAllow(req.userId)}>Allow</Button>
                    <Button onClick={() => onDeny(req.userId)}>Deny</Button>
                  </div>
                </div>
              ))}
            </Panel>
          )}
          {localConsent.length > 0 && (
            <Panel
              header={
                <div className={styles.permissionsDrawerTitle}>
                  <span>Recording Consent ({localConsent.length})</span>
                </div>
              }
              key={pendingScreenShareRequests.length > 0 ? "2" : "1"}
            >
              <div className={styles.consentList}>
                {localConsent.map((participant) => (
                  <div
                    key={participant.participantId}
                    className={styles.consentItem}
                  >
                    <div className={styles.participantInfo}>
                      <Avatar className={styles.participantAvatar}>
                        {generateAvatar(participant.participantName)}
                      </Avatar>
                      <div className={styles.participantDetails}>
                        <p className={styles.participantName}>
                          {participant.participantName}
                        </p>
                        <p
                          className={`${styles.consentStatus} ${getStatusClass(
                            participant.consent
                          )}`}
                        >
                          <span className={`${styles.statusIcon} ${styles[`statusIcon${participant.consent}`]}`}>
                            {getStatusIcon(participant.consent)}
                          </span>
                          {getStatusText(participant.consent)}
                        </p>
                      </div>
                    </div>
                    {participant.consent !== "accept" && (
                      <div
                        className={styles.refreshIcon}
                        onClick={() =>
                          handleRefreshConsent(participant.participantId)
                        }
                      >
                        <ReloadOutlined />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Panel>
          )}
        </Collapse>
      )}
      {(pendingScreenShareRequests.length === 0 && localConsent.length === 0) && (
        <div className={styles.permissionsDrawerEmpty}>
          <EmptyDrawerVector />
          <span>No pending requests.</span>
        </div>
      )}
    </SideDrawer>
  );
}
