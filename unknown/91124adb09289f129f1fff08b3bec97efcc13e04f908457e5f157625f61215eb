import React from 'react';
import { Input, Switch, Upload, Button } from 'antd';
import './CustomInput.scss';

const CustomInput = ({
  type = 'text',
  label,
  value,
  onChange,
  placeholder,
  required = false,
  error = '',
  maxLength,
  disabled = false,
  className = '',
  // For color picker
  colorValue,
  onColorChange,
  // For upload
  uploadProps,
  // For switch
  switchValue,
  onSwitchChange,
  switchLabel,
  // For button
  buttonText,
  buttonType = 'default',
  onButtonClick,
  buttonDisabled = false,
  // For custom styling
  customStyle = {},
  // For additional props
  ...props
}) => {
  const renderInput = () => {
    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'url':
        return (
          <fieldset className={`custom-fieldset ${error ? 'error' : ''} ${className}`} style={customStyle}>
            <legend className="custom-legend">
              {label} {required && <span className="required">*</span>}
            </legend>
            <Input
              type={type}
              className="custom-input"
              placeholder={placeholder}
              value={value}
              onChange={onChange}
              maxLength={maxLength}
              disabled={disabled}
              {...props}
            />
            {error && <div className="error-message">{error}</div>}
          </fieldset>
        );

      case 'color':
        return (
          <div className="color-picker-container">
            <span className="color-label">{label}:</span>
            <div className="color-picker-wrapper">
              <div 
                className="color-swatch" 
                style={{ backgroundColor: colorValue }}
              />
              <Input
                className="color-input"
                value={colorValue}
                onChange={onColorChange}
                placeholder={placeholder}
                disabled={disabled}
                {...props}
              />
            </div>
          </div>
        );

      case 'upload':
        return (
          <Upload.Dragger {...uploadProps} className="upload-area">
            <p className="upload-icon">
              <img src={props.uploadIcon} alt="Upload" />
            </p>
            <p className="upload-text">
              Drop your file here, Or <span className="browse-link">Browse</span>
            </p>
            <p className="upload-info">
              {props.uploadInfo || 'Supports PNG, JPG, GIF, SVG (Max 5MB)'}
            </p>
          </Upload.Dragger>
        );

      case 'switch':
        return (
          <div className="toggle-setting">
            <div className="toggle-container">
              <Switch
                checked={switchValue}
                onChange={onSwitchChange}
                size="default"
                disabled={disabled}
              />
              <span className="toggle-status">{switchLabel || (switchValue ? 'Enabled' : 'Disabled')}</span>
            </div>
          </div>
        );

      case 'button':
        return (
          <Button 
            className={`coming-soon-button ${className}`}
            disabled={buttonDisabled}
            onClick={onButtonClick}
            type={buttonType}
            style={customStyle}
            {...props}
          >
            {buttonText}
          </Button>
        );

      default:
        return (
          <fieldset className={`custom-fieldset ${error ? 'error' : ''} ${className}`} style={customStyle}>
            <legend className="custom-legend">
              {label} {required && <span className="required">*</span>}
            </legend>
            <Input
              className="custom-input"
              placeholder={placeholder}
              value={value}
              onChange={onChange}
              maxLength={maxLength}
              disabled={disabled}
              {...props}
            />
            {error && <div className="error-message">{error}</div>}
          </fieldset>
        );
    }
  };

  return renderInput();
};

export default CustomInput; 