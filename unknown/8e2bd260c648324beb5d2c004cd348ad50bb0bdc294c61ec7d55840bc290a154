import React from "react";
import QueueIcon from "../Assets/QueueIcon";
import InProgressIcon from "../Assets/InProgressIcon";
import FailedIcon from "../Assets/FailedIcon";
import UnavailableIcon from "../Assets/UnavailableIcon";
import "./StatusCard.scss";

// EmptyStateCard component that looks exactly like StatusCard but for empty states
export function EmptyStateCard({ type }) {
  const getEmptyStateContent = () => {
    switch (type) {
      case "key_insights":
        return {
          title: "Key Insights-Unavailable",
          description: "Insights unavailable — the recording may be too brief or lacked enough data."
        };
      case "summary":
        return {
          title: "Summary-Unavailable",
          description: "Couldn't pull a summary — maybe the session was quick or didn't have enough to go on."
        };
      case "action_items":
        return {
          title: "Action Items-Unavailable",
          description: "We weren't able to generate insights for this session. This can happen if the recording was too short or lacked sufficient data."
        };
      case "transcription":
        return {
          title: "Transcription-Unavailable",
          description: "No audio, video, or recording was found for this session. Please contact us for verification."
        };
      default:
        return {
          title: "Content-Unavailable",
          description: "No content available for this section."
        };
    }
  };

  const { title, description } = getEmptyStateContent();
  // Use unavailable styling for all empty state cards
  const meta = { colorClass: 'unavailable', icon: <UnavailableIcon /> };

  return (
    <div className={`status-card status-card-${meta.colorClass}`}>
      <div className="status-card-outer">
        <span className="status-card-icon">{meta.icon}</span>
        <span className="status-card-title">{title}</span>
        <span className="status-card-description">{description}</span>
      </div>
    </div>
  );
}

const statusMeta = {
  queued:      { colorClass: 'queued',      icon: <QueueIcon /> },
  transcription_inprogress: { colorClass: 'transcription_inprogress', icon: <InProgressIcon className="spinning" /> },
  in_process:  { colorClass: 'transcription_inprogress', icon: <InProgressIcon className="spinning" /> },
  inprocess:   { colorClass: 'transcription_inprogress', icon: <InProgressIcon className="spinning" /> },
  analysis_inprogress: { colorClass: 'transcription_inprogress', icon: <InProgressIcon className="spinning" /> },
  failed:      { colorClass: 'failed',      icon: <FailedIcon /> },
  analysis_failed: { colorClass: 'failed',  icon: <FailedIcon /> },
  transcription_failed: { colorClass: 'failed', icon: <FailedIcon /> },
  unavailable: { colorClass: 'unavailable', icon: <UnavailableIcon /> },
  not_available: { colorClass: 'not_available', icon: <UnavailableIcon /> },
  'not available': { colorClass: 'not_available', icon: <UnavailableIcon /> },
};

export default function StatusCard({ status, analysisType }) {
  const getStatusContent = () => {
    switch (status) {
      case "COMPLETED":
      case "completed":
        return {
          title: "Completed",
          description: "Your meeting has been transcribed successfully."
        };

      case "QUEUED":
      case "queued":
        // Different titles based on analysis type
        if (analysisType === "summary" || analysisType === "key_insights" || analysisType === "action_items") {
          return {
            title: "Analysis-In Queue",
            description: "Kindly please wait while your recording is being queued for analysis."
          };
        } else {
          return {
            title: "Transcription-In Queue",
            description: "Kindly please wait while your recording is being queued for transcription."
          };
        }

      case "transcription_inprogress":
      case "IN_PROCESS":
      case "inprocess":
        return {
          title: "Transcription-In Progress",
          description: "Transcription in progress. It'll be available here soon — check back shortly!"
        };

      case "transcription_failed":
      case "FAILED":
      case "failed":
        return {
          title: "Uh-oh! Transcript Took a Day Off",
          description: "Oops! We couldn't process the transcript. Raise a ticket and we'll handle it!"
        };

      case "analysis_inprogress":
        // Different titles based on analysis type
        if (analysisType === "summary") {
          return {
            title: "Summary Analysis-In Progress",
            description: "Cooking up your summary! Insights will be ready soon — hang tight!"
          };
        } else if (analysisType === "key_insights") {
          return {
            title: "Key Insights Analysis-In Progress",
            description: "We're analyzing the recording. Insights will be ready soon. Thanks for waiting!"
          };
        } else {
          return {
            title: "Analysis-In Progress",
            description: "We're analyzing the recording. Results will be ready soon. Thanks for waiting!"
          };
        }

      case "analysis_failed":
        // Different titles based on analysis type
        if (analysisType === "summary") {
          return {
            title: "Uh-oh! Couldn't Crack The Summary",
            description: "Oops! We lost the summary on the way. Let us know and we'll fetch it!"
          };
        } else if (analysisType === "key_insights") {
          return {
            title: "Uh-oh! Couldn't find the Insights",
            description: "Something went wrong. Tap below to raise a ticket — we'll sort it out!"
          };
        } else {
          return {
            title: "Uh-oh! Analysis Failed",
            description: "Something went wrong. Tap below to raise a ticket — we'll sort it out!"
          };
        }

      case "not_available":
      case "not available":
        return {
          title: "Not Available",
          description: "Insights unavailable — subscription not available."
        };

      case "unavailable":
      case "undefined":
      case "null":
      case undefined:
      case null:
        return {
          title: "Transcription-Unavailable",
          description: "No audio, video, or recording was found for this session. Please contact us for verification."
        };

      default:
        return {
          title: "Transcription-Unavailable",
          description: "No audio, video, or recording was found for this session. Please contact us for verification."
        };
    }
  };

  const { title, description } = getStatusContent();
  // Pick color/icon based on normalized status
  const normalizedStatus = String(status).toLowerCase().replace(/\s/g, '_');
  const meta = statusMeta[normalizedStatus] || statusMeta.unavailable;

  return (
    <div className={`status-card status-card-${meta.colorClass}`}>
      <div className="status-card-outer">
        <span className="status-card-icon">{meta.icon}</span>
        <span className="status-card-title">{title}</span>
        <span className="status-card-description">{description}</span>
      </div>
    </div>
  );
}