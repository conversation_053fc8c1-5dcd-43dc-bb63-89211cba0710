import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import { Button, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { CustomInput } from '../../../CommonComponents';
import './Typography.scss';

function Typography() {
  const [selectedFont, setSelectedFont] = useState('Inter');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const fontOptions = [
    { key: 'Inter', label: 'Inter' },
    { key: 'Roboto', label: 'Roboto' },
    { key: 'Open Sans', label: 'Open Sans' },
    { key: 'Lato', label: 'Lato' },
    { key: 'Poppins', label: 'Poppins' }
  ];

  const handleFontSelect = (font) => {
    setSelectedFont(font.key);
    setDropdownOpen(false);
  };

  const menuItems = fontOptions.map(font => ({
    key: font.key,
    label: (
      <div
        className={`font-option ${selectedFont === font.key ? 'selected' : ''}`}
        onClick={() => handleFontSelect(font)}
      >
        {font.label}
      </div>
    )
  }));

  return (
    <div className="typography-wrapper">
      <div className="typography">
        {/* Choose Font Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Choose font</h3>
              <p className="setting-description">
                Supports the primary color for elements like cards and carousels.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <Dropdown
                menu={{ items: menuItems }}
                trigger={['click']}
                placement="bottomLeft"
                open={dropdownOpen}
                onOpenChange={setDropdownOpen}
                overlayClassName="font-dropdown"
              >
                <div className="font-select-trigger">
                  <span className="font-select-text">{selectedFont}</span>
                  <DownOutlined className="font-select-arrow" />
                </div>
              </Dropdown>
            </div>
          </Col>
        </Row>

        {/* Upload Custom Font Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Upload Custom font</h3>
              <p className="setting-description">
                Upload specific zip file for a font
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="button"
                buttonText="Coming soon"
                buttonDisabled
              />
            </div>
          </Col>
        </Row>

        {/* Tooltip Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Tooltip</h3>
              <p className="setting-description">
                Supports the primary color for elements like cards and carousels.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="button"
                buttonText="Choose from library"
                buttonType="default"
                customStyle={{
                  background: '#fff',
                  border: '1px solid #e9ecef',
                  color: '#333',
                  width: '200px',
                  height: 'auto',
                  borderRadius: '6px',
                  fontSize: '14px',
                  padding: '8px 16px',
                  fontWeight: '500',
                  lineHeight: '1.5',
                  display: 'block',
                  margin: '0 auto',
                  textAlign: 'center'
                }}
              />
            </div>
          </Col>
        </Row>

        {/* Action Buttons */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="action-buttons">
              <Button type="primary" className="update-button">
                Update
              </Button>
              <Button className="cancel-button">
                Cancel
              </Button>
              <Button className="load-preview-button">
                Load Preview
              </Button>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default Typography; 