.profile-settings-wrapper {
  .settings-section {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;

    h4 {
      color: #333;
      margin-bottom: 8px;
      font-weight: 600;
    }

    p {
      color: #666;
      margin-bottom: 24px;
    }
  }

  .form-section {
    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
      }

      .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        &::placeholder {
          color: #999;
        }
      }

      textarea.form-control {
        resize: vertical;
        min-height: 100px;
      }
    }

    .form-actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;

      .btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &.btn-primary {
          background: #007bff;
          color: white;

          &:hover {
            background: #0056b3;
          }
        }

        &.btn-secondary {
          background: #6c757d;
          color: white;

          &:hover {
            background: #545b62;
          }
        }
      }
    }
  }
} 