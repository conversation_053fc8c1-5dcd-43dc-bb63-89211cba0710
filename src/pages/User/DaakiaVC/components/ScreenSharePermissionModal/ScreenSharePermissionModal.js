import React from "react";
import "./ScreenSharePermissionModal.scss";
import { <PERSON><PERSON>, Modal } from "antd";
import { DataReceivedEvent } from "../../utils/constants";

export default function ScreenSharePermissionModal({
  open,
  setOpen,
  requests = [],
  // onCancel,
  room,
  setPendingScreenShareRequests,
  title = "Screen Share Requests",
}) {
  

  // const { openDrawer } = useContext(DrawerContext);
  const onAllow = (id) => {
    const encoder = new TextEncoder();
    const encodedMessage = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.REQUEST_SCREEN_SHARE_PERMISSION_RESPONSE,
        request_by: id,
        is_screen_share_allowed: true,
      })
    );
    room.localParticipant.publishData(encodedMessage, {
      reliable: true,
      destinationIdentities: [id],
    });
    setPendingScreenShareRequests((prev) =>
      prev.filter((req) => req.userId !== id)
    );
    setOpen(false);
  };
  const onDeny = (id) => {
    const encoder = new TextEncoder();
    const encodedMessage = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.REQUEST_SCREEN_SHARE_PERMISSION_RESPONSE,
        request_by: id,
        is_screen_share_allowed: false,
      })
    );
    room.localParticipant.publishData(encodedMessage, {
      reliable: true,
      destinationIdentities: [id],
    });
    setPendingScreenShareRequests((prev) =>
      prev.filter((req) => req.userId !== id)
    );
    setOpen(false);
  };

  return (
    <Modal
      className="action-modal"
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      footer={null}
    >
      {requests.length > 1 && (
        <div className="req-count">{requests.length}</div>
      )}
      <span className="action-modal-content-title">{title}</span>
      <div className="action-modal-content-requests">
        {requests.length === 0 ? (
          <p>No pending requests.</p>
        ) : (
          requests.map((req) => (
            <div
              key={req.userId}
              className="action-modal-content"
              style={{ marginBottom: 16 }}
            >
              <span className="action-modal-content-description">
                <span>
                  {req.userName.split(" ")[0].length > 10
                    ? `${req.userName.split(" ")[0].substring(0, 10)}...`
                    : req.userName.split(" ")[0]}{" "}
                  {req.userName.split(" ").length > 1 &&
                    `${req.userName.split(" ")[1].charAt(0)}.`}
                </span>
              </span>
              <div className="action-modal-content-buttons">
                <Button onClick={() => onAllow(req.userId)}>Allow</Button>
                <Button onClick={() => onDeny(req.userId)}>Deny</Button>
              </div>
            </div>
          ))
        )}
      </div>
    </Modal>
  );
}
