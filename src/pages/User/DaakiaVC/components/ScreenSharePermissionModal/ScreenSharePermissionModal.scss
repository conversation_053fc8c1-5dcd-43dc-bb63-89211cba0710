$modal-background-color: #1e1e1e;
$allow-button-color: #3B60E4;
$font: "Inter", sans-serif;

.action{
    &-modal{
        width: 20rem !important;
        .req-count{
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            font-family: $font;
            position: absolute;
            top: -0.7rem;
            left: -0.7rem;
            background-color: $allow-button-color;
            border-radius: 50%;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .ant-modal-body{
            padding-top: 1.5rem;
            padding-bottom: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }
        .ant-modal-content{
            border-radius: 1rem;
            background-color: $modal-background-color;
            box-shadow: 0 0 5px 0 rgba(193, 193, 193, 0.1);
        }
        .ant-modal-close{
            color: #fff;
            top: 0.8rem;
            right: 0.3rem;
        }
        &-content{
            color: #fff;
            font-family: $font;
            display: flex;
            // flex-direction: column;
            justify-content: space-between;
            width: 100%;
            align-items: center;
            gap: 0.3rem;
            &-requests{
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.3rem;
                max-height: 9.5rem;
                overflow-y: auto;
                width: 100%;
                padding-right: 0.8rem;
                &::-webkit-scrollbar{
                    width: 5px;
                }
                &::-webkit-scrollbar-thumb{
                    background-color: #909090;
                    border-radius: 5px;
                }
                &::-webkit-scrollbar-track{
                    background-color: $modal-background-color;
                    border-radius: 5px;
                }
            }
            &-title{
                font-size: 18px;
                color: #fff;
                font-family: $font;
                font-weight: 600;
            }
            &-description{
                font-size: 14px;
                font-weight: 400;
                max-width: 6.6rem;
            }
            &-buttons{
                display: flex;
                justify-content: flex-end;
                gap: 1rem;
                .ant-btn{
                    border-radius: 0.5rem;
                    cursor: pointer;
                    &:nth-of-type(1){
                        background-color: $allow-button-color;
                        color: #fff;
                        border: none;
                        &:hover{
                            background-color: #7694ff;
                        }
                    }
                    &:nth-of-type(2){
                        background-color: transparent;
                        color: #fff;
                        &:hover{
                            border-color: #fff;
                            background-color: #fff;
                            color: #000;
                        }
                    }
                }
            }
        }
    }
}

.recording-permission{
    margin: 0;
    span{
        font-weight: 600;
    }
}