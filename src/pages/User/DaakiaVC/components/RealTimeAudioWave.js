import { useEffect, useRef, useState } from 'react';
import { useLocalParticipant, useIsSpeaking } from '@livekit/components-react';
import '../styles/RealTimeAudioWave.scss';

function RealTimeAudioWave({ className = '' }) {
  const { localParticipant } = useLocalParticipant();
  const isSpeaking = useIsSpeaking(localParticipant);
  const [showInitialCircle, setShowInitialCircle] = useState(false);
  const [showDots, setShowDots] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const hasShownInitialAnimation = useRef(false);

  const isActive = localParticipant?.isMicrophoneEnabled;

  // Handle microphone state changes for initial animation
  useEffect(() => {
    if (isActive && !hasShownInitialAnimation.current) {
      // First time mic is turned on - show circle-to-dots animation
      setShowInitialCircle(true);
      setShowDots(false);
      setIsTransitioning(true);
      hasShownInitialAnimation.current = true;

      // Show bars during the explosion animation
      const dotsTimer = setTimeout(() => {
        setShowDots(true);
      }, 800); // Start bars when particles begin settling

      // Hide circle and end transition after animation completes
      const circleTimer = setTimeout(() => {
        setShowInitialCircle(false);
        setIsTransitioning(false);
      }, 2500); // Full explosion animation duration

      return () => {
        clearTimeout(dotsTimer);
        clearTimeout(circleTimer);
      };
    } else if (isActive && hasShownInitialAnimation.current) {
      // Mic is on and we've already shown initial animation - just show bars
      setShowInitialCircle(false);
      setShowDots(true);
    }
    // When mic is off, we'll show a static circle (handled in render)
  }, [isActive]);

  // Create different height values for each bar to create equalizer effect
  const speakingLevel = isSpeaking ? 1 : 0;
  const bar1Height = 4 + (speakingLevel * 3);  // Base 4px, up to 7px when speaking (shorter)
  const bar2Height = 4 + (speakingLevel * 6);  // Base 4px, up to 10px when speaking (tallest)
  const bar3Height = 4 + (speakingLevel * 4);  // Base 4px, up to 8px when speaking (medium)

  return (
    <div
      className={`audio-dots ${className} ${isActive ? 'active' : 'inactive'} ${isSpeaking ? 'speaking' : ''} ${isTransitioning ? 'transitioning' : ''}`}
      style={{ '--audio-level': speakingLevel }}
    >
      {/* Show initial circle animation when mic first turns on */}
      {showInitialCircle && (
        <div className="initial-circle" />
      )}

      {/* Show third particle during transition */}
      {isTransitioning && (
        <div className="third-particle" />
      )}

      {/* Show dots when mic is on and animation is complete */}
      {showDots && (
        <>
          <div
            className="dot"
            style={{
              '--dot-scale-x': 1,
              '--dot-scale-y': bar1Height / 4, // Convert height to scale factor
              '--border-radius': isSpeaking ? '1px' : '50%' // Straight lines when speaking
            }}
          />
          <div
            className="dot"
            style={{
              '--dot-scale-x': 1,
              '--dot-scale-y': bar2Height / 4, // Convert height to scale factor
              '--border-radius': isSpeaking ? '1px' : '50%' // Straight lines when speaking
            }}
          />
          <div
            className="dot"
            style={{
              '--dot-scale-x': 1,
              '--dot-scale-y': bar3Height / 4, // Convert height to scale factor
              '--border-radius': isSpeaking ? '1px' : '50%' // Straight lines when speaking
            }}
          />
        </>
      )}

      {/* Show static circle when mic is off */}
      {!isActive && !showInitialCircle && !showDots && (
        <div className="static-circle" />
      )}
    </div>
  );
}

export default RealTimeAudioWave;
