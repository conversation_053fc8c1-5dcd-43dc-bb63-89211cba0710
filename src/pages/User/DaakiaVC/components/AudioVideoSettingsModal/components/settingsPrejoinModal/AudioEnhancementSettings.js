import React from 'react';
import { Switch } from 'antd';

/**
 * AudioEnhancementSettings Component
 * Audio enhancement controls (noise cancellation, echo cancellation, auto-mute, auto audio off)
 * @param {boolean} noiseCancellation - Noise cancellation state
 * @param {function} onNoiseCancellationChange - Noise cancellation change handler
 * @param {boolean} isNoiseSuppressionEnabled - Custom noise suppression state
 * @param {function} onNoiseSuppressionChange - Custom noise suppression change handler
 * @param {boolean} echoCancellation - Echo cancellation state
 * @param {function} onEchoCancellationChange - Echo cancellation change handler
 * @param {boolean} autoMuteOnJoin - Auto-mute on join state
 * @param {function} onAutoMuteOnJoinChange - Auto-mute on join change handler
 * @param {boolean} autoAudioOff - Auto audio off joining meeting state
 * @param {function} onAutoAudioOffChange - Auto audio off change handler
 */
function AudioEnhancementSettings({
  noiseCancellation = false,
  onNoiseCancellationChange,
  isNoiseSuppressionEnabled = false,
  onNoiseSuppressionChange,
  echoCancellation = false,
  onEchoCancellationChange,
  // autoMuteOnJoin = true,
  // onAutoMuteOnJoinChange,
  autoAudioOff = false,
  onAutoAudioOffChange
}) {
  return (
    <div className="settings-section">
      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Noise cancellation</span>
            <span className="setting-sublabel">Remove background noise to improve call quality</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={noiseCancellation}
            onChange={onNoiseCancellationChange}
          />
          <span className="switch-status">
            {noiseCancellation ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
      <div className="setting-border" />

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Advanced noise suppression</span>
            <span className="setting-sublabel">Enhanced AI-powered noise suppression for better audio quality</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={isNoiseSuppressionEnabled}
            onChange={() => onNoiseSuppressionChange(!isNoiseSuppressionEnabled)}
          />
          <span className="switch-status">
            {isNoiseSuppressionEnabled ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
      <div className="setting-border" />

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Echo cancellation</span>
            <span className="setting-sublabel">Remove background noise to improve call quality</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={echoCancellation}
            onChange={onEchoCancellationChange}
          />
          <span className="switch-status">
            {echoCancellation ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
      <div className="setting-border" />

      {/* <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Auto-mute on joining meeting</span>
            <span className="setting-sublabel">Automatically mute your microphone when joining the meeting</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={autoMuteOnJoin}
            onChange={onAutoMuteOnJoinChange}
          />
          <span className="switch-status">
            {autoMuteOnJoin ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div> */}
      {/* <div className="setting-border" /> */}

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Auto-mute on joining meeting</span>
            <span className="setting-sublabel">Automatically mute your microphone when joining the meeting</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={autoAudioOff}
            onChange={onAutoAudioOffChange}
          />
          <span className="switch-status">
            {autoAudioOff ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
    </div>
  );
}

export default AudioEnhancementSettings;
