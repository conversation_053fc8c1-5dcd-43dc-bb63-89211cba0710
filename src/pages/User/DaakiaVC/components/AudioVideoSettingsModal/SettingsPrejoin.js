/* eslint-disable */
import React, { useState, useEffect, useRef } from "react";
import { Modal, Tabs } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { useMediaQuery } from "../../hooks/useMediaQuerry";
import { useNoiseSuppressionContext } from "../../context/indexContext";
import { ReactComponent as BlueMicIcon } from "./Assets/bluemic.svg";
import { ReactComponent as BlueVideoIcon } from "./Assets/bluevid.svg";
import { ReactComponent as GreyMicIcon } from "./Assets/greymic.svg";
import { ReactComponent as GreyVideoIcon } from "./Assets/greyvid.svg";
import soundTestAudio from "./Assets/soundtest.mp3";


// Import extracted components
import {
  MicrophoneSettings,
  SpeakerSettings,
  CameraSettings,
  AudioEnhancementSettings,
  VideoEnhancementSettings
} from "./components/settingsPrejoinModal";

import "./SettingsPrejoin.scss";

export default function SettingsPrejoin({
  open,
  setOpen,
  // Audio props
  audioDeviceId,
  setAudioDeviceId,
  audioTrack,
  audioEnabled,
  setAudioEnabled,
  // Video props
  videoDeviceId,
  setVideoDeviceId,
  // Speaker props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Settings props
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  // Volume props
  outputVolume = 100,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff = false,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff = false,
  onAutoAudioOffChange,
  // Permission props
  permissions = { camera: false, microphone: false },
  // Virtual Background Modal props
  setIsVisualEffectsModalOpen,
}) {
  // Responsive breakpoints
  const isMobile = useMediaQuery("(max-width: 767px)");
  const isTabletOrBelow = useMediaQuery("(max-width: 1023px)");
  const isLargeScreen = useMediaQuery("(min-width: 1024px)");

  // Active tab state
  const [activeTab, setActiveTab] = useState("audio");

  // Device states
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);

  // Audio level states
  const [currentAudioLevel, setCurrentAudioLevel] = useState(0);

  // Test mic states
  const [isMicTesting, setIsMicTesting] = useState(false);
  const [micOriginalState, setMicOriginalState] = useState(null); // Store original mic state

  // Audio settings states
  const [noiseCancellation, setNoiseCancellation] = useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression || false
  );

  // Add custom noise suppression context
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();

  const [echoCancellation, setEchoCancellation] = useState(
    room?.options?.audioCaptureDefaults?.echoCancellation || false
  );
  const [autoMuteOnJoin, setAutoMuteOnJoin] = useState(true);

  // Single audio monitoring system
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);
  const testTimeoutRef = useRef(null);
  const testAudioEl = useRef(null);
  const speakerTestIntervalRef = useRef(null);

  // Scrollbar visibility refs
  const scrollTimeoutRef = useRef(null);

  // Test speaker states
  const [isSpeakerTesting, setIsSpeakerTesting] = useState(false);
  const [speakerAudioLevel, setSpeakerAudioLevel] = useState(0);

  // Fetch available devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

      
        setAudioDevices(audioInputs);
        setVideoDevices(videoInputs);
        setSpeakerDevices(audioOutputs);

        // Set default speaker if none selected and devices are available
        if (audioOutputs.length > 0 && !speakerDeviceId && setSpeakerDeviceId) {
          setSpeakerDeviceId(audioOutputs[0].deviceId);
        }
      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    };

    if (open) {
      fetchDevices();

      // Apply current volume setting to existing audio/video elements when modal opens
      setTimeout(() => {
        const { audioCount, videoCount } = applyVolumeToAllElements(outputVolume);
        // if (audioCount > 0 || videoCount > 0) {
        
        // }
      }, 100); // Small delay to ensure elements are rendered
    }
  }, [open, permissions, speakerDeviceId, setSpeakerDeviceId, outputVolume]);

  // // Track speaker device state changes
  // useEffect(() => {
  //   console.log('📊 Speaker device state changed to:', speakerDeviceId);
  // }, [speakerDeviceId]);

  // Apply speaker device setting to existing audio/video elements when modal opens or speakerDeviceId changes
  useEffect(() => {
    if (!open || !speakerDeviceId || speakerDeviceId.trim() === "") return;

    // COMMENTED OUT: Manual speaker device application - LiveKit handles this automatically
    // const applySpeakerDeviceToExistingElements = async () => {
    //   try {
    //     const audioElements = document.querySelectorAll('audio');
    //     const videoElements = document.querySelectorAll('video');
        
    //     // Apply to existing audio elements
    //     for (const audio of audioElements) {
    //       if (audio.setSinkId) {
    //         try {
    //           await audio.setSinkId(speakerDeviceId);
    //         } catch (error) {
    //           console.warn('Failed to set audio output device for existing audio element:', error);
    //         }
    //       }
    //     }
        
    //     // Apply to existing video elements
    //     for (const video of videoElements) {
    //       if (video.setSinkId) {
    //         try {
    //           await video.setSinkId(speakerDeviceId);
    //         } catch (error) {
    //           console.warn('Failed to set audio output device for existing video element:', error);
    //         }
    //       }
    //     }
        
    //     console.log('Successfully applied speaker device to existing elements in prejoin.');
    //   } catch (error) {
    //     console.error('Error applying speaker device to existing elements:', error);
    //   }
    // };

    // // Small delay to ensure elements are rendered
    // setTimeout(applySpeakerDeviceToExistingElements, 100);

    // LiveKit handles device switching automatically via useMediaDeviceSelect
    // No need for manual device application
    // console.log('Speaker device available:', speakerDeviceId);
  }, [open, speakerDeviceId]);

  // Watch for new audio/video elements and apply volume to them
  useEffect(() => {
    if (!open) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is an audio or video element
            if (node.tagName === 'AUDIO' || node.tagName === 'VIDEO') {
              node.volume = outputVolume / 100;
              
              // COMMENTED OUT: Manual speaker device application - LiveKit handles this automatically
              // if (speakerDeviceId && speakerDeviceId.trim() !== "" && node.setSinkId) {
              //   try {
              //     node.setSinkId(speakerDeviceId);
              //   } catch (error) {
              //     console.warn('Failed to set audio output device for new element:', error);
              //   }
              // }
            }

            // Check for audio/video elements within the added node
            const audioElements = node.querySelectorAll && node.querySelectorAll('audio, video');
            if (audioElements) {
              audioElements.forEach((element) => {
                element.volume = outputVolume / 100;
                
                // COMMENTED OUT: Manual speaker device application - LiveKit handles this automatically
                // if (speakerDeviceId && speakerDeviceId.trim() !== "" && element.setSinkId) {
                //   try {
                //     element.setSinkId(speakerDeviceId);
                //   } catch (error) {
                //     console.warn('Failed to set audio output device for new element:', error);
                //   }
                // }
              });
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [open, outputVolume]);

  // Unified audio level monitoring - ONLY during explicit testing
  useEffect(() => {
    const shouldMonitor = open && audioTrack && isMicTesting; // Only monitor during explicit testing

    if (!shouldMonitor) {
      // Clean up and reset level when not monitoring
      setCurrentAudioLevel(0);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing AudioContext:', error);
        }
        audioContextRef.current = null;
      }
      return;
    }

    const setupAudioMonitoring = async () => {
      try {
        // Clean up existing context first
        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
          try {
            audioContextRef.current.close();
          } catch (error) {
            console.warn('Error closing existing AudioContext:', error);
          }
        }
        audioContextRef.current = null;

        const stream = audioTrack.mediaStream;
        if (!stream) return;

        audioContextRef.current = new AudioContext();
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();

        // Optimized settings for audio monitoring
        analyserRef.current.fftSize = 2048;
        analyserRef.current.smoothingTimeConstant = isMicTesting ? 0.1 : 0.3; // More responsive during testing
        source.connect(analyserRef.current);

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

        const updateLevel = () => {
          if (!analyserRef.current || !audioContextRef.current || audioContextRef.current.state === 'closed') {
            return;
          }

          analyserRef.current.getByteTimeDomainData(dataArray);

          // Calculate RMS (Root Mean Square) for audio level
          let sum = 0;
          for (let i = 0; i < dataArray.length; i += 1) {
            const sample = (dataArray[i] - 128) / 128;
            sum += sample * sample;
          }
          const rms = Math.sqrt(sum / dataArray.length);

          // Convert to percentage with scaling
          let level = rms * 100 * (isMicTesting ? 8 : 6); // Higher sensitivity during testing

          // Apply logarithmic scaling for more natural response
          if (level > 0) {
            level = Math.log10(level + 1) * (isMicTesting ? 50 : 45);
          }

          level = Math.min(100, Math.max(0, level));
          setCurrentAudioLevel(level);

          // Continue monitoring if conditions are still met - ONLY during testing
          const stillShouldMonitor = open && audioTrack && isMicTesting;
          if (stillShouldMonitor) {
            animationFrameRef.current = requestAnimationFrame(updateLevel);
          }
        };

        updateLevel();
      } catch (error) {
        console.error('Error setting up audio monitoring:', error);
        setCurrentAudioLevel(0);
      }
    };

    setupAudioMonitoring();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing AudioContext during cleanup:', error);
        }
        audioContextRef.current = null;
      }
    };
  }, [open, audioTrack, isMicTesting]); // Removed audioEnabled dependency

  // Handle scrollbar visibility
  useEffect(() => {
    const handleScroll = (event) => {
      const element = event.target;
      element.classList.add('scrolling');

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        element.classList.remove('scrolling');
      }, 1000);
    };

    if (open) {
      const scrollableElements = document.querySelectorAll('.ant-tabs-tabpane, .settings-content');

      scrollableElements.forEach(element => {
        element.addEventListener('scroll', handleScroll, { passive: true });
      });

      return () => {
        scrollableElements.forEach(element => {
          element.removeEventListener('scroll', handleScroll);
        });

        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [open]);

  // Simplified test microphone function
  const testMicrophone = async () => {
    if (isMicTesting) {
      // Stop testing
      setIsMicTesting(false);

      // Clear test timeout
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      // Restore original microphone state if we changed it
      if (micOriginalState !== null) {
        setAudioEnabled(micOriginalState);
        setMicOriginalState(null);
      }

      return;
    }

    // Start testing
    if (!audioEnabled) {
      setMicOriginalState(false); // Remember it was off
      setAudioEnabled(true);

      // Wait a bit for the audio track to be available
      setTimeout(() => {
        if (audioTrack) {
          setIsMicTesting(true);
        }
      }, 500);
    } else {
      // Mic was already on, just start testing
      setMicOriginalState(true); // Remember it was on
      setIsMicTesting(true);
    }

    // Auto-stop test after 10 seconds
    testTimeoutRef.current = setTimeout(() => {
      if (isMicTesting) {
        setIsMicTesting(false);

        // Restore original state
        if (micOriginalState !== null) {
          setAudioEnabled(micOriginalState);
          setMicOriginalState(null);
        }
      }
    }, 10000);
  };

  // Clean up when modal closes
  useEffect(() => {
    if (!open) {
      // Stop any ongoing test
      setIsMicTesting(false);
      setIsSpeakerTesting(false);

      // Stop speaker test audio
      if (testAudioEl.current) {
        testAudioEl.current.pause();
        testAudioEl.current = null;
      }
      if (speakerTestIntervalRef.current) {
        clearInterval(speakerTestIntervalRef.current);
        speakerTestIntervalRef.current = null;
      }

      // Clear timeouts
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      // Restore original mic state if needed
      if (micOriginalState !== null) {
        setAudioEnabled(micOriginalState);
        setMicOriginalState(null);
      }

      // Cleanup gain node
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }

      // Reset audio levels
      setCurrentAudioLevel(0);
      setSpeakerAudioLevel(0);
    }
  }, [open]);

  // Handle device changes
  const handleAudioDeviceChange = (deviceId) => {
    setAudioDeviceId(deviceId);
  };

  const handleVideoDeviceChange = (deviceId) => {
    setVideoDeviceId(deviceId);
  };

  const handleSpeakerDeviceChange = async (deviceId) => {
    if (setSpeakerDeviceId) {
      console.log('🔄 Before state update - speakerDeviceId:', speakerDeviceId);
      console.log('🎯 New device selected:', deviceId);
      
      // LiveKit handles device switching automatically via useMediaDeviceSelect
      // Just update our state for UI consistency
      setSpeakerDeviceId(deviceId);
      
      console.log('✅ After setSpeakerDeviceId called with:', deviceId);
      
      // COMMENTED OUT: Manual setSinkId call - LiveKit handles this automatically
      // try {
      //   const audioElements = document.querySelectorAll('audio');
      //   const videoElements = document.querySelectorAll('video');
      
      //   for (const audio of audioElements) {
      //     if (audio.setSinkId) {
      //       await audio.setSinkId(deviceId);
      //     }
      //   }
      
      //   for (const video of videoElements) {
      //     if (video.setSinkId) {
      //       await video.setSinkId(deviceId);
      //     }
      //   }
      
      //   console.log('Successfully switched speaker device to:', deviceId);
      // } catch (error) {
      //   console.error('Error switching speaker device:', error);
      // }
      
      console.log('🎉 Speaker device change handler completed');
    } else {
      console.warn('⚠️ setSpeakerDeviceId is not available');
    }
  };

  // Handle audio settings changes
  const handleNoiseCancellation = (checked) => {
    setNoiseCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.noiseSuppression = checked;
    }
  };

  const handleEchoCancellation = (checked) => {
    setEchoCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.echoCancellation = checked;
    }
  };

  // Helper function to apply volume to all media elements
  const applyVolumeToAllElements = (volume) => {
    const audioElements = document.querySelectorAll('audio');
    audioElements.forEach(audio => {
      audio.volume = volume / 100;
    });

    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      video.volume = volume / 100;
    });

    return { audioCount: audioElements.length, videoCount: videoElements.length };
  };

  // Handle output volume changes - control system volume and test audio
  const handleOutputVolumeChange = async (value) => {
    // Call the parent component's volume change handler
    if (onOutputVolumeChange) {
      onOutputVolumeChange(value);
    }
  };

  const testSpeaker = async () => {
    if (isSpeakerTesting) {
      // Stop testing
      if (testAudioEl.current) {
        testAudioEl.current.pause();
        testAudioEl.current = null;
      }
      if (speakerTestIntervalRef.current) {
        clearInterval(speakerTestIntervalRef.current);
        speakerTestIntervalRef.current = null;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      return;
    }

    // Start testing
    setIsSpeakerTesting(true);
    console.log('🔊 Starting speaker test with device:', speakerDeviceId);

    try {
      // Create a test audio element with the imported sound
      const audio = new Audio(soundTestAudio);
      testAudioEl.current = audio;
      audio.volume = outputVolume / 100; // Use the volume slider value
      
      // Set the audio output device if supported and specified
      if (speakerDeviceId && speakerDeviceId !== 'audiooutput' && audio.setSinkId) {
        try {
          console.log('🎯 Attempting to set speaker device to:', speakerDeviceId);
          
          // Validate the device ID first
          const devices = await navigator.mediaDevices.enumerateDevices();
          const audioOutputDevices = devices.filter(device => device.kind === 'audiooutput');
          const deviceExists = audioOutputDevices.some(device => device.deviceId === speakerDeviceId);
          
          if (deviceExists) {
            await audio.setSinkId(speakerDeviceId);
            console.log('✅ Successfully set speaker device to:', speakerDeviceId);
          } else {
            console.warn('⚠️ Speaker device not found, using default');
            // Try to fallback to default
            await audio.setSinkId('default');
          }
        } catch (error) {
          console.warn('❌ Failed to set audio output device:', error);
        }
      } else {
        console.log('ℹ️ Using default speaker (no specific device set)');
      }
      
      // Clear any previous interval
      if (speakerTestIntervalRef.current) {
        clearInterval(speakerTestIntervalRef.current);
      }

      // Start the level simulation
      speakerTestIntervalRef.current = setInterval(() => {
        // Simulate audio level with some variation
        const level = Math.random() * 80 + 20; // Random level between 20-100
        setSpeakerAudioLevel(level);
      }, 100);


      // Play the audio
      await audio.play();

      // Stop simulation when audio ends
      audio.addEventListener('ended', () => {
        if (speakerTestIntervalRef.current) {
          clearInterval(speakerTestIntervalRef.current);
          speakerTestIntervalRef.current = null;
        }
        setSpeakerAudioLevel(0);
        setIsSpeakerTesting(false);
        if (testAudioEl.current) {
          testAudioEl.current = null;
        }
      });
      
    } catch (error) {
      console.error('Error playing test sound:', error);
      // Cleanup on error
      if (speakerTestIntervalRef.current) {
        clearInterval(speakerTestIntervalRef.current);
        speakerTestIntervalRef.current = null;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      if (testAudioEl.current) {
          testAudioEl.current = null;
      }
    }
  };

  // Dynamic tab label component
  const TabLabel = ({ tabKey, icon: { blue: BlueIcon, grey: GreyIcon }, text }) => {
    const isActive = activeTab === tabKey;
    const IconComponent = isActive ? BlueIcon : GreyIcon;

    return (
      <div className="tab-label">
        <IconComponent />
        <span>{text}</span>
      </div>
    );
  };

  // Tab items configuration
  const tabItems = [
    {
      key: 'audio',
      label: (
        <TabLabel
          tabKey="audio"
          icon={{ blue: BlueMicIcon, grey: GreyMicIcon }}
          text="Audio"
        />
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <div className="settings-header">
              <h3>Audio Settings</h3>
              <p className="settings-description">Change your audio settings here</p>
            </div>
          </div>

          {/* Microphone Section */}
          <MicrophoneSettings
            audioDevices={audioDevices}
            audioDeviceId={audioDeviceId}
            onAudioDeviceChange={handleAudioDeviceChange}
            permissions={permissions}
            onTestMicrophone={testMicrophone}
            isMicTesting={isMicTesting}
            audioEnabled={audioEnabled}
            micOriginalState={micOriginalState}
            currentAudioLevel={currentAudioLevel}
          />

          {/* Speaker Section */}
          <SpeakerSettings
            speakerDevices={speakerDevices}
            speakerDeviceId={speakerDeviceId}
            onSpeakerDeviceChange={handleSpeakerDeviceChange}
            permissions={permissions}
            onTestSpeaker={testSpeaker}
            isSpeakerTesting={isSpeakerTesting}
            speakerAudioLevel={speakerAudioLevel}
            outputVolume={outputVolume}
            onOutputVolumeChange={handleOutputVolumeChange}
          />

          {/* Audio Enhancement Section */}
          <AudioEnhancementSettings
            noiseCancellation={noiseCancellation}
            onNoiseCancellationChange={handleNoiseCancellation}
            isNoiseSuppressionEnabled={isNoiseSuppressionEnabled}
            onNoiseSuppressionChange={setIsNoiseSuppressionEnabled}
            echoCancellation={echoCancellation}
            onEchoCancellationChange={handleEchoCancellation}
            autoMuteOnJoin={autoMuteOnJoin}
            onAutoMuteOnJoinChange={setAutoMuteOnJoin}
            autoAudioOff={autoAudioOff}
            onAutoAudioOffChange={onAutoAudioOffChange}
          />
        </div>
      ),
    },
  ];

  return (
    <Modal
      open={open}
      onOk={() => setOpen(false)}
      onCancel={() => setOpen(false)}
      className={`settings-prejoin-modal ${isMobile ? 'mobile-modal' : ''} ${isTabletOrBelow && !isMobile ? 'tablet-modal' : ''}`}
      footer={null}
      width={isMobile ? "100vw" : "auto"} // Full viewport width for mobile
      height={isMobile ? "100vh" : undefined}
      style={isMobile ? {
        maxWidth: '100vw',
        width: '100vw',
        margin: 0,
        padding: 0,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        height: '100vh',
        position: 'fixed',
        transform: 'none'
      } : {
        maxWidth: '95vw', // Prevent exceeding viewport
        width: 'auto' // Auto width
      }}
      closeIcon={null}
      centered={!isMobile}
      destroyOnClose={false}
      maskClosable={!isMobile}
      title={
        <div className="custom-modal-header">
          <span className="custom-modal-title">Settings</span>
          <div className="custom-close-button" onClick={(e) => {
            e.stopPropagation();
            setOpen(false);
          }}>
            <CloseOutlined />
          </div>
        </div>
      }
    >
      <Tabs
        defaultActiveKey="audio"
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        tabPosition={isLargeScreen ? "left" : "top"}
        className="settings-tabs"
      />
    </Modal>
  );
}