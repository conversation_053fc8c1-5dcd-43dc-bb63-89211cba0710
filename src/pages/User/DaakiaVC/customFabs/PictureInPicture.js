import { useRef, useState, useCallback, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, useTracks, isTrackReference, useParticipants, TrackToggle, DisconnectButton } from '@livekit/components-react';
import { generateAvatar, parseMetadata } from '../utils/helper';
import { useAutomaticPiPContext } from '../context/indexContext';
// import { ScreenCaptureButton } from '../components/settings/ScreenCapture/ScreenCaptureButton';
import '../styles/PictureInPicture.scss';
import { ReactComponent as EndCallIconPip } from './icons/endcalliconPip.svg';

// Speaking priority configuration
const SPEAKING_PRIORITY_CONFIG = {
  STICKY_DURATION: 5000, // Keep speaking participants visible for 5 seconds after they stop speaking
  UPDATE_INTERVAL: 500,   // Check for speaking changes every 500ms
};

// Speaking animation styles - reusable constants
const SPEAKING_ANIMATION_STYLES = {
  base: {
    position: 'absolute',
    background: '#1e8cfa',
    borderRadius: '50%',
    zIndex: 2,
    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  },
  large: {
    width: '1.5vmin',
    height: '1.5vmin',
  },
  small: {
    width: '1vmin',
    height: '1vmin',
  }
};

function SpeakingIndicator({ size = 'large' }) {
  const sizeStyle = size === 'large' ? SPEAKING_ANIMATION_STYLES.large : SPEAKING_ANIMATION_STYLES.small;
  const position = size === 'large' ? '1vmin' : '0.5vmin';

  return (
    <>
      <div style={{
        ...SPEAKING_ANIMATION_STYLES.base,
        ...sizeStyle,
        bottom: position,
        left: position,
        animationDelay: '0.6s'
      }} />
      <div style={{
        ...SPEAKING_ANIMATION_STYLES.base,
        ...sizeStyle,
        bottom: position,
        right: position,
        animationDelay: '0.9s'
      }} />
    </>
  );
}

// Reusable Remote Participant Tile Component
function RemoteParticipantTile({ participant, track, name, color, showVideo = true, keyPrefix = 'remote' }) {
  const isRemoteSpeaking = participant?.isSpeaking || false;

  return (
    <div
      key={participant.identity || `${keyPrefix}-${Math.random()}`}
      className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
    >
      {showVideo && track?.publication && !track.publication.isMuted ? (
        <div className="pip-video-container">
          <VideoTrack
            trackRef={track}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              borderRadius: '2vmin'
            }}
          />
        </div>
      ) : (
        <div className="pip-tile-avatar-center">
          <div
            className="pip-tile-avatar pip-tile-avatar-small"
            style={{ backgroundColor: color }}
          >
            {name}
          </div>
        </div>
      )}
      {isRemoteSpeaking && <SpeakingIndicator size="small" />}
    </div>
  );
}

function SimplePipContent({
  localParticipant,
  controlPosition = 'bottom'
}) {

  const allParticipants = useParticipants();

  // State for speaking priority tracking
  const [speakingHistory, setSpeakingHistory] = useState(new Map());



  // Get camera and screen share tracks for video display
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
    { source: Track.Source.ScreenShare, withPlaceholder: false },
  ]);

  // Memoized track filtering for better performance
  const { localCameraTrack, screenShareTracks, isScreenShareActive, activeScreenShareTrack } = useMemo(() => { // eslint-disable-line no-unused-vars
    const localCamera = allTracks
      .filter(isTrackReference)
      .find((track) =>
        track.participant.isLocal &&
        track.source === Track.Source.Camera
      );

    const screenShares = allTracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    const isActive = screenShares.some((track) =>
      track.publication.isSubscribed && !track.publication.isMuted
    );

    const activeTrack = screenShares.find((track) =>
      track.publication.isSubscribed && !track.publication.isMuted
    );

    return {
      localCameraTrack: localCamera,
      screenShareTracks: screenShares,
      isScreenShareActive: isActive,
      activeScreenShareTrack: activeTrack
    };
  }, [allTracks]);


  // Optimized speaking detection with simpler dependencies
  useEffect(() => {
    const speakingParticipants = allParticipants.filter(p => p.isSpeaking);
    if (speakingParticipants.length === 0) return;

    const now = Date.now();
    setSpeakingHistory(prev => {
      const updated = new Map(prev);
      speakingParticipants.forEach(p => updated.set(p.identity, now));
      return updated;
    });
  }, [allParticipants]);

  // Advanced Speaking Priority System with Sticky Behavior
  const getAdvancedSpeakingPriorityParticipants = useCallback((participants, maxCount) => {
    const now = Date.now();
    const currentSpeaking = participants.filter(p => p.isSpeaking);
    const currentSpeakingIds = new Set(currentSpeaking.map(p => p.identity));

    // Get recently speaking participants (within sticky duration)
    const recentlySpeaking = participants.filter(p => {
      const lastSpeakingTime = speakingHistory.get(p.identity);
      return lastSpeakingTime && (now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION;
    });

    // Priority order: Currently speaking > Recently speaking > Others
    const currentlySpakingParticipants = participants.filter(p => currentSpeakingIds.has(p.identity));
    const recentlySpokingParticipants = recentlySpeaking.filter(p => !currentSpeakingIds.has(p.identity));
    const otherParticipants = participants.filter(p =>
      !currentSpeakingIds.has(p.identity) &&
      !recentlySpeaking.some(rp => rp.identity === p.identity)
    );

    // Build final list with priority
    const prioritizedList = [
      ...currentlySpakingParticipants,
      ...recentlySpokingParticipants,
      ...otherParticipants
    ];

    // Return up to maxCount participants
    return prioritizedList.slice(0, maxCount);
  }, [speakingHistory]);

  // Periodic update for speaking priority (cleanup old speaking history)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setSpeakingHistory(prev => {
        const updated = new Map();
        prev.forEach((lastSpeakingTime, participantId) => {
          // Keep only recent speaking history
          if ((now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION * 2) {
            updated.set(participantId, lastSpeakingTime);
          }
        });
        return updated;
      });
    }, SPEAKING_PRIORITY_CONFIG.UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [setSpeakingHistory]);

  // Get remote participants with advanced speaking priority
  // When screen sharing: max 2 remotes, when no screen share: max 3 remotes
  const maxRemotes = isScreenShareActive ? 2 : 3;
  const allRemoteParticipants = allParticipants.filter((participant) => !participant.isLocal);
  const remoteParticipants = getAdvancedSpeakingPriorityParticipants(allRemoteParticipants, maxRemotes);



  // Create remote participant data with their camera tracks and info (if any)
  const remoteParticipantsWithTracks = useMemo(() => {
    return remoteParticipants.map((participant) => {
      const cameraTrack = allTracks
        .filter(isTrackReference)
        .find((track) =>
          track.participant.identity === participant.identity &&
          track.source === Track.Source.Camera
        );

      // Pre-compute participant info to avoid hooks in render
      const name = participant.name
        ? generateAvatar(participant.name)
        : generateAvatar(participant.identity);

      let color = '#7C4DFF';
      try {
        const metaColor = parseMetadata(participant.metadata)?.color;
        if (metaColor) color = metaColor;
      } catch (e) { /* ignore */ }

      return {
        participant,
        track: cameraTrack || null,
        name,
        color
      };
    });
  }, [remoteParticipants, allTracks]);



  // Check if local participant is speaking
  const isSpeaking = localParticipant?.isSpeaking || false;

  // Get participant count for dynamic grid (now based on all remote participants)
  const participantCount = remoteParticipants.length;

  // Dynamic grid class based on participant count, screen share status, and control position
  const getGridClass = (count, controlPos, hasScreenShare) => {
    const baseClass = controlPos === 'top' ? 'control-top' : 'control-bottom';
    const screenSharePrefix = hasScreenShare ? 'pip-grid-screenshare' : 'pip-grid';

    if (hasScreenShare) {
      // WITH SCREEN SHARE layouts
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;
        case 1: return `${screenSharePrefix}-two ${baseClass}`;
        case 2: return `${screenSharePrefix}-three ${baseClass}`;
        default: return `${screenSharePrefix}-three ${baseClass}`;
      }
    } else {
      // NO SCREEN SHARE layouts (your existing perfect logic)
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;
        case 1: return `${screenSharePrefix}-two ${baseClass}`;
        case 2: return `${screenSharePrefix}-three ${baseClass}`;
        case 3: return `${screenSharePrefix}-four ${baseClass}`;
        default: return `${screenSharePrefix}-four ${baseClass}`;
      }
    }
  };




  // Get local participant info
  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }


  const ControlTile = useMemo(() => {
    return (
      <div className="pip-tile pip-control-tile">
        <div className="pip-control-content">
          <div className="pip-control-buttons">
            <TrackToggle
              source={Track.Source.Microphone}
              showIcon
              className="pip-control-button"
            />
            <TrackToggle
              source={Track.Source.Camera}
              showIcon
              className="pip-control-button"
            />
            {/* Only show screen share button when screen sharing is active */}
            {isScreenShareActive && (
              <TrackToggle
                source={Track.Source.ScreenShare}
                showIcon
                className="pip-control-button"
              />
            )}
            <DisconnectButton
              className="pip-control-button pip-disconnect-button"
            >
              <EndCallIconPip />
            </DisconnectButton>
          </div>
        </div>
      </div>
    );
  }, [isScreenShareActive]);

  return (
    <div className="pip-main-container">
      <div className={`pip-grid-container ${getGridClass(participantCount, controlPosition, isScreenShareActive)}`}>




        {isScreenShareActive ? (
          <>            {/* Screen Share Tile - Always the big tile on top */}
            <div className="pip-tile pip-tile-screenshare">
              {activeScreenShareTrack ? (
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={activeScreenShareTrack}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                  
                  {/* {(isHost || isCoHost) && (
                    <div className="pip-screen-capture-button">
                      <ScreenCaptureButton
                        room={room}
                        screenShareTracks={screenShareTracks}
                        focusTrack={activeScreenShareTrack}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setShowPopover={() => {}} // No popover in PiP
                      />
                    </div>
                  )} */}
                </div>
              ) : (
                <div className="pip-tile-center-dot" />
              )}
            </div>

            {/* Local Participant Tile - Below screen share */}
            {/* MEMORY OPTIMIZATION: When screen sharing, show only avatar for local participant (no video) */}
            <div className={`pip-tile pip-tile-small pip-tile-local ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
              {/* Always show avatar for local participant when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
              {isSpeaking && <SpeakingIndicator size="small" />}
            </div>
          </>
        ) : (
          // NO SCREEN SHARE: Local participant as main tile
          <div className={`pip-tile pip-tile-main ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
            {localCameraTrack?.publication && !localCameraTrack.publication.isMuted ? (
              // Show video when camera is on
              <div className="pip-video-container">
                <VideoTrack
                  trackRef={localCameraTrack}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    borderRadius: '2vmin'
                  }}
                />
              </div>
            ) : (
              // Show avatar when camera is off
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
            )}

            {/* Corner accent lights for local speaking */}
            {isSpeaking && <SpeakingIndicator size="large" />}
          </div>
        )}

        {/* Dynamic remote participant tiles - Only show when NO screen share */}
        {!isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => (
          <RemoteParticipantTile
            key={participantData.participant.identity || `remote-${index}`}
            participant={participantData.participant}
            track={participantData.track}
            name={participantData.name}
            color={participantData.color}
            showVideo
            keyPrefix="remote"
          />
        ))}

        {/* Dynamic remote participant tiles - Only show when WITH screen share */}
        {/* MEMORY OPTIMIZATION: When screen sharing, show only avatars for remote participants */}
        {isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => (
          <RemoteParticipantTile
            key={participantData.participant.identity || `remote-screenshare-${index}`}
            participant={participantData.participant}
            track={participantData.track}
            name={participantData.name}
            color={participantData.color}
            showVideo={false}
            keyPrefix="remote-screenshare"
          />
        ))}

          {/* Control tile - positioned at bottom if controlPosition is 'bottom' */}
          {controlPosition === 'bottom' && ControlTile}
        </div>
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  localParticipant,
  room, // eslint-disable-line no-unused-vars
  controlPosition = 'bottom',
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Automatic PiP context
  const {
    isAutomaticPiPEnabled,
    setIsAutomaticPiPEnabled,
    hasAskedPermission,
    setHasAskedPermission,
    setIsAutomaticPiPSupported,
    setAutomaticPiPFunctions
  } = useAutomaticPiPContext();

  // Get participant count for dynamic sizing
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
  ]);
  const remoteTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => !track.participant.isLocal);
  const participantCount = remoteTracks.length;

  // Optimized dynamic PiP window configuration with useCallback and lookup object
  const getDynamicConfig = useCallback((count) => {
    const configs = {
      0: { width: 180, height: 297 },  // Solo - minimum size
      1: { width: 220, height: 363 },  // Two participants - small window
      2: { width: 280, height: 429 },  // Three participants - medium window
      3: { width: 320, height: 495 },  // Four participants - larger window
    };
    return configs[count] || { width: 320, height: 528 }; // Default for 4+ participants
  }, []);

  const defaultConfig = useMemo(() => getDynamicConfig(participantCount), [participantCount]);

  // Track previous participant count for notifications
  const prevParticipantCountRef = useRef(participantCount);

  // Auto-resize disabled to prevent browser errors
  // Users can manually resize the PiP window if needed
  useEffect(() => {
    prevParticipantCountRef.current = participantCount;
  }, [participantCount]);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Check if automatic PiP is supported
  const isAutomaticPiPSupported = useMemo(() => {
    const hasMediaSession = 'mediaSession' in navigator && 'setActionHandler' in navigator.mediaSession;

    // Check Chrome version (automatic PiP requires Chrome 120+)
    const isChrome = /Chrome/.test(navigator.userAgent);
    const chromeVersion = isChrome ? parseInt(navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || '0') : 0;

    return hasMediaSession && isChrome && chromeVersion >= 120;
  }, []);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Forward declaration for openPipWindow
  const openPipWindowRef = useRef(null);

  // Store the action handler for manual testing
  const automaticPiPHandlerRef = useRef(null);

  // Ask for automatic PiP permission
  const askForAutomaticPiPPermission = useCallback(async () => {
    if (!isAutomaticPiPSupported) {
      return false;
    }

    try {
      // Create the handler function
      const handler = async () => {
        // This will be called when user switches tabs
        if (openPipWindowRef.current) {
          try {
            await openPipWindowRef.current();
          } catch (error) {
            console.error('Error in automatic PiP trigger:', error);
            // Turn off auto PiP when error occurs
            setIsAutomaticPiPEnabled(false);
          }
        }
      };

      // Store handler for manual testing and register it
      automaticPiPHandlerRef.current = handler;
      navigator.mediaSession.setActionHandler('enterpictureinpicture', handler);

      // Set media session metadata to enhance user experience
      if ('mediaSession' in navigator) {
        navigator.mediaSession.metadata = new MediaMetadata({
          title: 'Video Conference',
          artist: 'Daakia',
          album: 'Live Meeting',
          artwork: [
            { src: '/favicon.ico', sizes: '96x96', type: 'image/x-icon' }
          ]
        });

        // Set playback state to "playing" - required for automatic PiP
        navigator.mediaSession.playbackState = 'playing';

        // Add media session handlers
        navigator.mediaSession.setActionHandler('play', () => {});
        navigator.mediaSession.setActionHandler('pause', () => {});
      }

      setIsAutomaticPiPEnabled(true);
      setHasAskedPermission(true);

      return true;
    } catch (error) {
      console.error('Failed to register automatic PiP:', error);
      // Turn off auto PiP when registration fails
      setIsAutomaticPiPEnabled(false);
      return false;
    }
  }, [isAutomaticPiPSupported, setIsAutomaticPiPEnabled, setHasAskedPermission]);

  // Toggle automatic PiP
  const toggleAutomaticPiP = useCallback(async (enabled) => {
    if (enabled) {
      return askForAutomaticPiPPermission();
    } else {
      try {
        // Remove the action handler to disable automatic PiP
        navigator.mediaSession.setActionHandler('enterpictureinpicture', null);
        setIsAutomaticPiPEnabled(false);
        return true;
      } catch (error) {
        console.error('Failed to disable automatic PiP:', error);
        return false;
      }
    }
  }, [askForAutomaticPiPPermission, setIsAutomaticPiPEnabled]);

  // Register automatic PiP functions with context
  useEffect(() => {
    setIsAutomaticPiPSupported(isAutomaticPiPSupported);
    setAutomaticPiPFunctions({
      askForAutomaticPiPPermission,
      toggleAutomaticPiP
    });
  }, [isAutomaticPiPSupported, askForAutomaticPiPPermission, toggleAutomaticPiP, setIsAutomaticPiPSupported, setAutomaticPiPFunctions]);

  // // Auto-enable automatic PiP on mount if supported and enabled by default
  useEffect(() => {
    if (isAutomaticPiPSupported && isAutomaticPiPEnabled && !hasAskedPermission) {
      // Automatically enable automatic PiP when component mounts
      askForAutomaticPiPPermission();
    }
  }, [isAutomaticPiPSupported, isAutomaticPiPEnabled, hasAskedPermission, askForAutomaticPiPPermission]);

  // Create a hidden media element for Chrome to detect
  const hiddenMediaRef = useRef(null);

  // Setup hidden media element for automatic PiP detection
  useEffect(() => {
    if (isAutomaticPiPEnabled && room?.localParticipant) {
      try {
        // Create a hidden audio element
        const audio = document.createElement('audio');
        audio.style.display = 'none';
        audio.muted = true; // Start muted to avoid audio feedback
        audio.autoplay = true;
        audio.loop = true;

        // Add error handler for audio element
        audio.addEventListener('error', () => {
          console.error('Hidden audio element error');
          setIsAutomaticPiPEnabled(false);
        });

        // Try to get the actual media stream from LiveKit
        const micPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
        if (micPublication?.track) {
          const mediaStream = new MediaStream([micPublication.track.mediaStreamTrack]);
          audio.srcObject = mediaStream;

        // Add to DOM first
        document.body.appendChild(audio);
        hiddenMediaRef.current = audio;

        // Try to play the audio element
        audio.play().then(() => {
          // Chrome might need AUDIBLE media for automatic PiP
          // Make it very quiet but audible
          setTimeout(() => {
            audio.muted = false;
            audio.volume = 0.01; // Very quiet but audible

            // Set media session position state
            if ('mediaSession' in navigator) {
              navigator.mediaSession.setPositionState({
                duration: 3600, // 1 hour
                playbackRate: 1.0,
                position: 0
              });
            }
          }, 1000);

        }).catch((error) => {
          console.error('Failed to play hidden audio element:', error);
          // Try unmuting and playing again
          audio.muted = false;
          audio.volume = 0.01;
          audio.play().catch((retryError) => {
            console.error('Failed to play audio on retry:', retryError);
            // Turn off auto PiP when audio setup fails
            setIsAutomaticPiPEnabled(false);
          });
        });
      }
      } catch (error) {
        console.error('Error setting up hidden media element:', error);
        setIsAutomaticPiPEnabled(false);
      }
    }

    return () => {
      if (hiddenMediaRef.current) {
        document.body.removeChild(hiddenMediaRef.current);
        hiddenMediaRef.current = null;
      }
    };
  }, [isAutomaticPiPEnabled, room?.localParticipant]);



  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent
      localParticipant={localParticipant}
      controlPosition={controlPosition}
    />;
  }, [localParticipant, controlPosition]);




  // Simple error handling
  const handlePipError = useCallback(() => {
    console.error("Failed to open Picture-in-Picture");
    // Turn off auto PiP when PiP window fails to open
    if (isAutomaticPiPEnabled) {
      setIsAutomaticPiPEnabled(false);
    }
  }, [isAutomaticPiPEnabled, setIsAutomaticPiPEnabled]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document and copy styles from main document
      const pipDoc = pipWindow.document;

      // Copy all stylesheets from the main document to PiP window
      const mainStyleSheets = Array.from(document.styleSheets);
      mainStyleSheets.forEach((styleSheet) => {
        try {
          if (styleSheet.href) {
            // External stylesheet
            const link = pipDoc.createElement('link');
            link.rel = 'stylesheet';
            link.href = styleSheet.href;
            pipDoc.head.appendChild(link);
          } else if (styleSheet.ownerNode) {
            // Inline stylesheet
            const clonedStyle = styleSheet.ownerNode.cloneNode(true);
            pipDoc.head.appendChild(clonedStyle);
          }
        } catch (e) {
          // Handle CORS issues with external stylesheets
          console.warn('Could not copy stylesheet:', e);
        }
      });

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Update the ref when openPipWindow changes
  useEffect(() => {
    openPipWindowRef.current = openPipWindow;
  }, [openPipWindow]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Track user gestures for engagement
  useEffect(() => {
    const trackUserGesture = () => {
      window.lastUserGestureTime = Date.now();
    };

    // Track various user interactions
    const events = ['click', 'keydown', 'touchstart', 'mousedown'];
    events.forEach(event => {
      document.addEventListener(event, trackUserGesture, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, trackUserGesture);
      });
    };
  }, []);



  // Clean up automatic PiP on unmount
  useEffect(() => {
    return () => {
      if (isAutomaticPiPEnabled) {
        try {
          navigator.mediaSession.setActionHandler('enterpictureinpicture', null);
        } catch (error) {
          console.warn('Failed to cleanup automatic PiP:', error);
        }
      }
    };
  }, [isAutomaticPiPEnabled]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported,
    controlPosition
  };
}