import React, { createContext, useContext, useState, useMemo } from 'react';

const PublicPinnedContext = createContext();
export function PublicPinnedProvider({ children }) {
  const [publicChatPinnedMessages, setPublicChatPinnedMessages] = useState(null);

  const contextValue = useMemo(() => ({
    publicChatPinnedMessages,
    setPublicChatPinnedMessages,
  }), [publicChatPinnedMessages]);

  return (
    <PublicPinnedContext.Provider value={contextValue}>
      {children}
    </PublicPinnedContext.Provider>
  );
}


const PrivatePinnedContext = createContext();
export function PrivatePinnedProvider({ children }) {
  const [privateChatPinnedMessages, setPrivateChatPinnedMessages] = useState(new Map());

  const contextValue = useMemo(() => ({
    privateChatPinnedMessages,
    setPrivateChatPinnedMessages,
  }), [privateChatPinnedMessages]);

  return (
    <PrivatePinnedContext.Provider value={contextValue}>
      {children}
    </PrivatePinnedContext.Provider>
  );
}

export function ChatProvider({ children }) {
  return (
    <PublicPinnedProvider>
      <PrivatePinnedProvider>
        {children}
      </PrivatePinnedProvider>
    </PublicPinnedProvider>
  );
}

export function usePublicPinned() {
  const context = useContext(PublicPinnedContext);
  if (!context) throw new Error('usePublicPinned must be used within PublicPinnedProvider');
  return context;
}

export function usePrivatePinned() {
  const context = useContext(PrivatePinnedContext);
  if (!context) throw new Error('usePrivatePinned must be used within PrivatePinnedProvider');
  return context;
}

