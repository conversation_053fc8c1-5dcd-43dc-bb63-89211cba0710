import React, { createContext, useContext, useMemo, useState } from "react";

const SaasConfigContext = createContext();

export function SaasConfigProvider({ children, saasConfig }) {
  const value = useMemo(
    () => ({
      saasConfig,
      isSaaS: saasConfig?.isSaaS || false,
      saasHostToken: saasConfig?.hostToken || null,
      saasMeetingId: saasConfig?.meetingId || null,
      saasMeetingConfig: saasConfig?.meetingConfig || null,
      saasMeetingFeatures: saasConfig?.meetingFeatures || null,
    }),
    [saasConfig]
  );

  return (
    <SaasConfigContext.Provider value={value}>
      {children}
    </SaasConfigContext.Provider>
  );
}

const SaasMeetingContext = createContext();

export function SaasMeetingProvider({ children }) {
  const [hasExtended, setHasExtended] = useState(false);
  const [autoMeetingEndTime, setAutoMeetingEndTime] = useState(null);

  const contextValue = useMemo(() => ({
    hasExtended,
    setHasExtended,
    autoMeetingEndTime,
    setAutoMeetingEndTime,
  }), [hasExtended, autoMeetingEndTime]);

  return (
    <SaasMeetingContext.Provider value={contextValue}>
      {children}
    </SaasMeetingContext.Provider>
  );
}

// Main provider that combines contexts
export function SaasProvider({ children, saasConfig }) {
  return (
    <SaasConfigProvider saasConfig={saasConfig}>
      <SaasMeetingProvider>
        {children}
      </SaasMeetingProvider>
    </SaasConfigProvider>
  );
}

// Individual hooks for each context
export function useSaasConfig() {
  const context = useContext(SaasConfigContext);
  if (!context) {
    throw new Error("useSaasConfig must be used within a SaasConfigProvider");
  }
  return context;
}

export function useSaasMeeting() {
  const context = useContext(SaasMeetingContext);
  if (!context) {
    throw new Error("useSaasMeeting must be used within a SaasMeetingProvider");
  }
  return context;
}
