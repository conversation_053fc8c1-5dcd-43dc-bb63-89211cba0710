import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';


export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessor = useRef(null);
  const originalTrack = useRef(null);

  const getMicState = () => {
    const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);

    return {
      micPublication,
      isEnabled: micPublication?.isEnabled || false,
      hasTrack: !!micPublication?.track,
      isMuted: micPublication?.isMuted || false,
      isPublished: micPublication?.track && !micPublication?.isMuted,
      hasNoiseProcessor: !!noiseProcessor.current
    };
  };

  const applyNoiseSuppression = async () => {
    try {
      const { micPublication } = getMicState();
      const localAudioTrack = micPublication.track;

      originalTrack.current = localAudioTrack.mediaStreamTrack;

      noiseProcessor.current = new NoiseSuppressionProcessor();
      const processedTrack = await noiseProcessor.current.startProcessing(
        localAudioTrack.mediaStreamTrack
      );

      if (processedTrack) {
        await localAudioTrack.replaceTrack(processedTrack, true);
      }
    } catch (error) {
      console.error('❌ Error applying noise suppression:', error);
    }
  };

  const stopNoiseSuppression = async () => {
    try {
      if (noiseProcessor.current) {
        const { micPublication } = getMicState();

        if (micPublication?.track && originalTrack.current) {
          await micPublication.track.replaceTrack(originalTrack.current, true);
        }

        noiseProcessor.current.stopProcessing();
        noiseProcessor.current = null;
        originalTrack.current = null;
      }
    } catch (error) {
      console.error('❌ Error stopping noise suppression:', error);
    }
  };

  const handleNoiseSuppressionLogic = async () => {
    const {
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor
    } = getMicState();

    // NS Toggle is OFF - stop if running
    if (!isNoiseSuppressionEnabled) {
      if (hasNoiseProcessor) {
        await stopNoiseSuppression();
      }
      return;
    }

    // NS Toggle is ON but no mic track
    if (!hasTrack) {
      return;
    }

    // NS Toggle is ON, mic exists but muted - keep processor if exists
    if (isMuted) {
      return;
    }

    // NS Toggle is ON, mic unmuted and published
    if (isPublished) {
      if (!hasNoiseProcessor) {
        setTimeout(async () => {
          try {
            await applyNoiseSuppression();
          } catch (error) {
            console.error('❌ Delayed noise suppression error:', error);
          }
        }, 1000);
      }
      return;
    }

    // Mic disabled - no action needed
    if (!isEnabled) {
      // No action needed when mic is disabled
    }
  };

  useEffect(() => {
    if (!room?.localParticipant) return;

    // When device changes, stop noise suppression first
    if (noiseProcessor.current) {
      stopNoiseSuppression();
    }

    // Then let the normal logic handle reapplying if needed
    setTimeout(() => {
      handleNoiseSuppressionLogic();
    }, 500);
  }, [deviceIdAudio]);

  useEffect(() => {
    if (!room?.localParticipant) return;
    handleNoiseSuppressionLogic();
  }, [
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isEnabled,
    isNoiseSuppressionEnabled,
    isNoiseSuppressionEnabled && !noiseProcessor.current ?
      room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isMuted : null
  ]);

  useEffect(() => {
    return () => {
      stopNoiseSuppression();
    };
  }, []);

  return {
    isNoiseSuppressionActive: !!noiseProcessor.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression
  };
};
