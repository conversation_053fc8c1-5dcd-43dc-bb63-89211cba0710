// $modal-background-color: #1e1e1e;
// $allow-button-color: #3B60E4;

// .screen-share-permission{
//     &-modal{
//         width: 20rem !important;
//         .ant-modal-body{
//             padding-top: 3rem;
//         }
//         .ant-modal-content{
//             border-radius: 1rem;
//             background-color: $modal-background-color;
//             box-shadow: 0 0 5px 0 rgba(193, 193, 193, 0.1);
//         }
//         .ant-modal-close{
//             color: #fff;
//         }
//     }
//     &-request{
//         color: #fff;
//         font-family: "Inter", sans-serif;
//         span{
//             font-weight: 600;
//         }
//         &:nth-child(1){
//             margin: 0;
//         }
//         &-buttons{
//             display: flex;
//             justify-content: flex-end;
//             gap: 1rem;
//             .ant-btn{
//                 border-radius: 0.5rem;
//                 cursor: pointer;
//                 &:nth-child(1){
//                     background-color: $allow-button-color;
//                     color: #fff;
//                     border: none;
//                 }
//                 &:nth-child(2){
//                     background-color: transparent;
//                     color: #000;
//                     border: none;
//                 }
//             }
//         }
//     }
// }