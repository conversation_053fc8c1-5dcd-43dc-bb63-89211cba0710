import React from 'react';
import { Button, Modal } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import './RecordingDeniedModal.scss';

export function RecordingDeniedModal({
    isOpen,
    onClose,
    onChangeToAgree
}) {
    return (
        <Modal 
            title={null} 
            open={isOpen} 
            onCancel={onClose}
            footer={null}
            // closable={false}
            className="recording-denied-modal"
            mask={false}
            maskClosable={false}
            transitionName=""
            maskTransitionName=""
        >
            <div className='denied-content'>
                {/* <Button className='close-button' onClick={onClose} icon={<CloseOutlined />} /> */}
                <div className='icon-container'>
                    <ExclamationCircleFilled />
                </div>
                <h2 className='denied-title'>Recording denied</h2>
                <p className='denied-message'>
                    You have denied to have this meeting recorded. Do you wish to change your response?
                </p>
                <div className='button-container'>
                    <Button className='change-button' onClick={onChangeToAgree}>
                        Change to agree
                    </Button>
                    <Button className='dismiss-button' onClick={onClose}>
                        Dismiss
                    </Button>
                </div>
            </div>
        </Modal>
    );
} 