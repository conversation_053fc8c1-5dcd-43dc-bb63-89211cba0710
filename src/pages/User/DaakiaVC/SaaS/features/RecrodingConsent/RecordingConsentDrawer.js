import React, { useEffect, useState } from "react";
import { Avatar } from "antd";
import {
    CheckOutlined,
    ExclamationOutlined,
    CloseOutlined,
    ReloadOutlined,
  } from "@ant-design/icons";
import SideDrawer from "../../../components/SideDrawer";
import { DataReceivedEvent } from "../../../utils/constants"; 
import { generateAvatar } from "../../../utils/helper";
import "./RecordingConsentDrawer.scss";

export function RecordingConsentDrawer({
    participantConsent,
    showRecordingConsentDrawer,
    room
}){
    // Add local state to track consent changes
  const [localConsent, setLocalConsent] = useState(participantConsent);

  // Update local state when participantConsent changes
  useEffect(() => {
    setLocalConsent(participantConsent);
  }, [participantConsent]);

  // Handler for refreshing participant consent
  const handleRefreshConsent = (participantId) => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.RECORDING_CONSENT_MODAL,
        value: true,
      })
    );
    room.localParticipant.publishData(data, {
      reliable: true,
      destinationIdentities: [participantId],
    });
  };

  const getStatusIcon = (status) => {
    if (status === "accept") {
      return (
        <span className="icon-box accepted">
          <CheckOutlined />
        </span>
      );
    }
    if (status === "reject") {
      return (
        <span className="icon-box denied">
          <CloseOutlined />
        </span>
      );
    }
    return (
      <span className="icon-box pending">
        <ExclamationOutlined />
      </span>
    );
  };

  const getStatusClass = (status) => {
    if (status === "accept") return "accepted";
    if (status === "reject") return "denied";
    return "pending";
  };

  const getStatusText = (status) => {
    if (status === "accept") return "Accepted";
    if (status === "reject") return "Denied";
    return "Pending";
  };

  return (
    <SideDrawer
      show={showRecordingConsentDrawer}
      title="Recording Consent"
    >
      <div className="consent-list">
        {localConsent.map((participant) => (
          <div key={participant.participantId} className="consent-item">
            <div className="participant-info">
              <Avatar className="participant-avatar">
                {generateAvatar(participant.participantName)}
              </Avatar>
              <div className="participant-details">
                <p className="participant-name">
                  {participant.participantName}
                </p>
                <p
                  className={`consent-status ${getStatusClass(
                    participant.consent
                  )}`}
                >
                  <span className="status-icon">
                    {getStatusIcon(participant.consent)}
                  </span>
                  {getStatusText(participant.consent)}
                </p>
              </div>
            </div>
            {participant.consent !== "accept" && (
              <div
                className="refresh-icon"
                onClick={() => handleRefreshConsent(participant.participantId)}
              >
                <ReloadOutlined />
              </div>
            )}
          </div>
        ))}
      </div>
    </SideDrawer>
  );
}