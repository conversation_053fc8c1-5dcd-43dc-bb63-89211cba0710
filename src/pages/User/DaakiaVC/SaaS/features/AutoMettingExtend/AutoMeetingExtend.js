import { useRef, useEffect, useState } from "react";
import moment from "moment";
import { SaasService } from "../../services/saasServices"; // Fixed path
import { DataReceivedEvent, constants } from "../../../utils/constants";
import { setLocalStorage } from "../../../utils/helper";
import { useSaasHelpers } from "../../helpers/helpers";
import { useSaasMeeting } from "../../../context/SaasConfigContext";

const AUTO_MEETING_SETTINGS = {
  EXTEND_MINUTES: 10,          
  WARNING_MINUTES: 5,          
  CHECK_INTERVAL_MS: 10000,  
  WARNING_INTERVAL_MS: 60000,   
};

export const useAutoMeetingEnd = ({meetingDetails, isHost, room, coHostToken, setIsMeetingFinished, setToastNotification, setToastStatus, setShowToast}) => {

  const { isSaaS, saasHostToken } = useSaasHelpers();

  const { hasExtended, setHasExtended, autoMeetingEndTime, setAutoMeetingEndTime } = useSaasMeeting();


  const [isExtending, setIsExtending] = useState(false);
  const [endMeetingNotification, setEndMeetingNotification] = useState(false);

  const shouldWork = isSaaS && meetingDetails?.meeting_config?.auto_meeting_end === 1;
  const initialEndTime = shouldWork ? moment(meetingDetails?.meeting_config?.auto_meeting_end_schedule) : null;
  const currentEndTime = autoMeetingEndTime || initialEndTime;





  const dismissed = useRef(false);
  const hasShownToast = useRef(false);
  const hasCheckedExtension = useRef(false);

  // Simple function to detect if meeting was already extended
  const detectIfAlreadyExtended = () => {
    if (!meetingDetails?.end_date || !meetingDetails?.meeting_config?.auto_meeting_end_schedule) {
      return false;
    }
    const originalEnd = new Date(meetingDetails.end_date).getTime();
    const scheduledEnd = new Date(meetingDetails.meeting_config.auto_meeting_end_schedule).getTime();
    return scheduledEnd > originalEnd;
  };

  const extendMeeting = async () => {
    if (hasExtended || isExtending) {
      setToastNotification("Meeting can only be extended once!");
      setToastStatus("warning");
      setShowToast(true);
      return;
    }

    setIsExtending(true);
    try {
      const response = await SaasService.extendMeeting(
        true,
        meetingDetails?.room_uid,
        saasHostToken || coHostToken
      );

      if (response?.success === 1) {
        setEndMeetingNotification(false);
        setHasExtended(true);

        const newEndTime = currentEndTime.clone().add(AUTO_MEETING_SETTINGS.EXTEND_MINUTES, "minutes");
        setAutoMeetingEndTime(newEndTime);
        dismissed.current = false;
        hasShownToast.current = false;

        const encoder = new TextEncoder();
        const data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.EXTEND_MEETING_END_TIME,
          })
        );
        room.localParticipant.publishData(data, { reliable: true });
        setToastNotification("Meeting extended successfully!");
        setToastStatus("success");
        setShowToast(true);
      } else {
        setToastNotification("Error extending meeting!");
        setToastStatus("error");
        setShowToast(true);
      }
    } catch (error) {
      setToastNotification("Error extending meeting!");
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsExtending(false);
    }
  };

  useEffect(() => {
    if (!room || !shouldWork || !currentEndTime) return;

    // Check once if meeting was already extended (on mount/refresh)
    if (!hasCheckedExtension.current) {
      const wasExtended = detectIfAlreadyExtended();
      if (wasExtended) {
        setHasExtended(true);
      }
      hasCheckedExtension.current = true;
    }

    const intervalId = setInterval(() => {
      const currentDateTime = moment();
      const timeDifference = currentEndTime.diff(currentDateTime, "seconds");

      if (timeDifference <= 0) {
        setIsMeetingFinished(() => true);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        room.disconnect();
        
      } else if (timeDifference <= AUTO_MEETING_SETTINGS.WARNING_MINUTES * 60) {
        if(isHost){
          if(!dismissed.current && !hasExtended){
            setEndMeetingNotification(true);
          }
        }
        if(!hasShownToast.current){
          setToastNotification(`Meeting will end in ${AUTO_MEETING_SETTINGS.WARNING_MINUTES} minutes`);
          setToastStatus("warning");
          setShowToast(true);
          hasShownToast.current = true;
        }
      }
    }, AUTO_MEETING_SETTINGS.CHECK_INTERVAL_MS); 

    return () => clearInterval(intervalId);
  }, [room, currentEndTime, shouldWork, hasExtended]);

  const handleExtendMeetingReceived = () => {
    setHasExtended(true);
    setAutoMeetingEndTime((prev) => (prev || initialEndTime).clone().add(AUTO_MEETING_SETTINGS.EXTEND_MINUTES, "minutes"));
    hasShownToast.current = false; 
    setToastNotification(`Meeting extended by ${AUTO_MEETING_SETTINGS.EXTEND_MINUTES} minutes`);
    setToastStatus("info");
    setShowToast(true);
  };

  return {
    autoMeetingEndTime: currentEndTime,
    setAutoMeetingEndTime,
    endMeetingNotification: shouldWork ? endMeetingNotification : false,
    setEndMeetingNotification,
    dismissed,
    hasShownToast,
    isExtending,
    hasExtended,
    extendMeeting: shouldWork ? extendMeeting : () => {},
    handleExtendMeetingReceived: shouldWork ? handleExtendMeetingReceived : () => {},
    isSaaSEnabled: isSaaS,
    shouldWork
  };
};
 