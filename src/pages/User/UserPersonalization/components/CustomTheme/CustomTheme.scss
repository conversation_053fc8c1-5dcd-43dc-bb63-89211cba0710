// Import color variables from parent component
$primary-blue: #3B60E4;
$text-dark: #333;
$text-muted: #666;
$background-light: #f8f9fa;
$background-white: #fff;
$border-color: #e9ecef;
$sidebar-active-bg: #E6EBF6;

.custom-theme-wrapper {
  .settings-section {
    width: 100%;
    
    .customTheme-section {
      width: 100%;
      
      // Tab Navigation Container
      .tab-navigation {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0 60px; // Space for arrows
        border-bottom: 1px solid $border-color;
        background: $background-white;
        
        // Left Arrow
        .nav-arrow-left {
          position: absolute;
          left: 16px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: $text-muted;
          font-size: 18px;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          transition: all 0.3s ease;
          
          &:hover {
            color: $primary-blue;
            background: $sidebar-active-bg;
          }
          
          &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            
            &:hover {
              color: $text-muted;
              background: none;
            }
          }
        }
        
        // Right Arrow
        .nav-arrow-right {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: $text-muted;
          font-size: 18px;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          transition: all 0.3s ease;
          
          &:hover {
            color: $primary-blue;
            background: $sidebar-active-bg;
          }
          
          &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            
            &:hover {
              color: $text-muted;
              background: none;
            }
          }
        }
        
        // Tabs Container
        .tabs-container {
          display: flex;
          align-items: center;
          gap: 32px;
          overflow-x: auto;
          scroll-behavior: smooth;
          padding: 16px 0;
          width: 100%;
          scrollbar-width: none; // Firefox
          -ms-overflow-style: none; // IE and Edge
          
          &::-webkit-scrollbar {
            display: none; // Chrome, Safari, Opera
          }
          
          .tab-item {
            background: none;
            border: none;
            color: $text-muted;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            padding: 8px 0;
            white-space: nowrap;
            transition: all 0.3s ease;
            position: relative;
            flex-shrink: 0; // Prevent tabs from shrinking
            
            &:hover {
              color: $text-dark;
            }
            
            &.active {
              color: $primary-blue;
              font-weight: 600;
              
              &::after {
                content: '';
                position: absolute;
                bottom: -16px;
                left: 0;
                right: 0;
                height: 2px;
                background: $primary-blue;
                border-radius: 1px;
              }
            }
          }
        }
      }
      
      // Tab Content Container
      .tab-container {
        padding: 24px;
        background: $background-white;
        min-height: 400px;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .settings-section {
      .customTheme-section {
        .tab-navigation {
          padding: 0 50px;
          
          .nav-arrow-left,
          .nav-arrow-right {
            font-size: 16px;
            padding: 6px;
          }
          
          .tabs-container {
            gap: 20px;
            padding: 12px 0;
            
            .tab-item {
              font-size: 13px;
              padding: 6px 0;
            }
          }
        }
        
        .tab-container {
          padding: 16px;
          min-height: 300px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .settings-section {
      .customTheme-section {
        .tab-navigation {
          padding: 0 40px;
          
          .nav-arrow-left,
          .nav-arrow-right {
            font-size: 14px;
            padding: 4px;
          }
          
          .tabs-container {
            gap: 16px;
            padding: 10px 0;
            
            .tab-item {
              font-size: 12px;
              padding: 4px 0;
            }
          }
        }
        
        .tab-container {
          padding: 12px;
          min-height: 250px;
        }
      }
    }
  }
} 