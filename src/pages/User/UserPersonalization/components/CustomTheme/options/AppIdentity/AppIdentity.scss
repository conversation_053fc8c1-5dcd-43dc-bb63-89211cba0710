// Import color variables from parent components
$primary-blue: #3B60E4;
$text-dark: #333;
$text-muted: #666;
$background-light: #f8f9fa;
$background-white: #fff;
$border-color: #e9ecef;
$sidebar-active-bg: #E6EBF6;
$success-green: #28a745;
$warning-yellow: #ffc107;
$danger-red: #dc3545;

.app-identity-wrapper {
  .app-identity {
    width: 100%;
    
    .setting-section {
      padding: 24px 0;
      border-bottom: 1px solid $border-color;
      display: flex;
      align-items: center;
      
      &:last-child {
        border-bottom: none;
      }
      
      .setting-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        
        .setting-title {
          color: $text-dark;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }
        
        .setting-description {
          color: $text-muted;
          font-size: 14px;
          line-height: 1.5;
          margin: 0;
        }
      }
      
      // .setting-control {
      //   // All input styling is now handled by CustomInput component
      // }
    }
    
    // Action Buttons Styles
    .action-buttons {
      display: flex;
      gap: 12px;
      justify-content: center;
      padding-top: 24px;
      
      .update-button {
        background: $primary-blue !important;
        border-color: $primary-blue !important;
        color: white !important;
        border-radius: 6px;
        padding: 8px 24px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.5;
        height: auto;
        min-width: 100px;
        
        &:hover {
          background: darken($primary-blue, 10%) !important;
          border-color: darken($primary-blue, 10%) !important;
        }
      }
      
      .cancel-button,
      .load-preview-button {
        background: white !important;
        border: 1px solid $border-color !important;
        color: $text-dark !important;
        border-radius: 6px;
        padding: 8px 24px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.5;
        height: auto;
        min-width: 100px;
        
        &:hover {
          border-color: $primary-blue !important;
          color: $primary-blue !important;
        }
      }
    }
    
    // Mobile responsive adjustments
    @media (max-width: 767px) {
      .setting-section {
        padding: 20px 0;
        
        .setting-info {
          margin-bottom: 16px;
          
          .setting-title {
            font-size: 15px;
          }
          
          .setting-description {
            font-size: 13px;
          }
        }
      }
      
      .action-buttons {
        flex-direction: column;
        gap: 8px;
        align-items: center;
        
        .update-button,
        .cancel-button,
        .load-preview-button {
          width: 200px;
          padding: 10px 24px;
          line-height: 1.4;
        }
      }
    }
  }
} 