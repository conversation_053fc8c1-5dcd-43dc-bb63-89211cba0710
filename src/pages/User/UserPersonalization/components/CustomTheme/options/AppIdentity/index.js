import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import { Button } from 'antd';
import { CustomInput } from '../../../CommonComponents';
import uploadIcon from '../../../../icons/upload.svg';
import './AppIdentity.scss';

function AppIdentity() {
  const [brandName, setBrandName] = useState('Daakia');
  const [showBrandName, setShowBrandName] = useState(true);
  const [customMeetingUrl, setCustomMeetingUrl] = useState('');
  const [customJoinButtonColor, setCustomJoinButtonColor] = useState('#9E9E9E');

  const handleBrandNameChange = ({ target: { value } }) => {
    if (value.length <= 60) {
      setBrandName(value);
    }
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.png,.jpg,.gif,.svg',
    beforeUpload: () => false, // Prevent auto upload
    showUploadList: false,
  };

  return (
    <div className="app-identity-wrapper">
      <div className="app-identity">
        {/* Brand Name Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Brand name</h3>
              <p className="setting-description">
                Replace &apos;Daakia&apos; with your company or product name across the UI (title bar, login page, meeting rooms, etc.)
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="text"
                label="Brand name"
                value={brandName}
                onChange={handleBrandNameChange}
                placeholder="Input Text"
                required
                maxLength={60}
              />
            </div>
          </Col>
        </Row>

        {/* Logo Upload Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Logo upload</h3>
              <p className="setting-description">
                Upload logo (shown in header, pre-meeting screens, recordings, etc.)
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="upload"
                uploadProps={uploadProps}
                uploadIcon={uploadIcon}
                uploadInfo="Supports PNG, JPG, GIF, SVG (Max 5MB)"
              />
            </div>
          </Col>
        </Row>

        {/* Favicon Upload Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Favicon Upload</h3>
              <p className="setting-description">
                Custom favicon to show in browser tab. Logo dimension with transparent background 24*24
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="upload"
                uploadProps={uploadProps}
                uploadIcon={uploadIcon}
                uploadInfo="Supports PNG, JPG, GIF, SVG (Max 5MB)"
              />
            </div>
          </Col>
        </Row>

        {/* Background Image Upload Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Background Image Upload</h3>
              <p className="setting-description">
                Supports the primary color for elements like cards and carousels.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="upload"
                uploadProps={uploadProps}
                uploadIcon={uploadIcon}
                uploadInfo="Supports PNG, JPG, GIF, SVG (Max 5MB)"
              />
            </div>
          </Col>
        </Row>

        {/* Enable Custom Lobby Branding Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Enable custom lobby branding</h3>
              <p className="setting-description">
                Upload specific zip file for a font
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="button"
                buttonText="Coming soon"
                buttonDisabled
              />
            </div>
          </Col>
        </Row>

        {/* Custom Join Button Color Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Custom join button color</h3>
              <p className="setting-description">
                Highlights key elements like buttons and calls to action.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="color"
                label="Custom color"
                colorValue={customJoinButtonColor}
                onColorChange={(e) => setCustomJoinButtonColor(e.target.value)}
                placeholder="#9E9E9E"
              />
            </div>
          </Col>
        </Row>

        {/* Custom Meeting URL Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Custom meeting URL</h3>
              <p className="setting-description">
                Create and share a personalized meeting link for quick and easy access.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="text"
                label="Meeting URL"
                value={customMeetingUrl}
                onChange={(e) => setCustomMeetingUrl(e.target.value)}
                placeholder="Input Text"
                required
              />
            </div>
          </Col>
        </Row>

        {/* Show Brand Name in Pre-join Page Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Show brand name in pre-join page</h3>
              <p className="setting-description">
                Show your brand name on the screen before participants join the meeting.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="switch"
                switchValue={showBrandName}
                onSwitchChange={setShowBrandName}
                switchLabel={showBrandName ? 'Enabled' : 'Disabled'}
              />
            </div>
          </Col>
        </Row>

        {/* Action Buttons */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="action-buttons">
              <Button type="primary" className="update-button">
                Update
              </Button>
              <Button className="cancel-button">
                Cancel
              </Button>
              <Button className="load-preview-button">
                Load Preview
              </Button>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default AppIdentity; 