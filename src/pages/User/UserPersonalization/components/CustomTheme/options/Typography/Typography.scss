// Import color variables from parent components
$primary-blue: #3B60E4;
$text-dark: #333;
$text-muted: #666;
$background-light: #f8f9fa;
$background-white: #fff;
$border-color: #e9ecef;
$sidebar-active-bg: #E6EBF6;

.typography-wrapper {
  .typography {
    width: 100%;
    
    .setting-section {
      padding: 24px 0;
      border-bottom: 1px solid $border-color;
      display: flex;
      align-items: center;
      
      &:last-child {
        border-bottom: none;
      }
      
      .setting-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        
        .setting-title {
          color: $text-dark;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }
        
        .setting-description {
          color: $text-muted;
          font-size: 14px;
          line-height: 1.5;
          margin: 0;
        }
      }

      .setting-control {
        width: 100%;
        display: flex;
        justify-content: flex-end;

        .font-select-trigger {
          width: 200px;
          height: auto;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          background: #fff;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 16px;
          cursor: pointer;
          transition: all 0.2s ease;
          margin: 0 auto;

          .font-select-text {
            font-size: 14px;
            color: #333;
            line-height: 1.5;
            font-weight: 500;
            text-align: center;
            flex: 1;
          }

          .font-select-arrow {
            color: #666;
            font-size: 12px;
            transition: transform 0.2s ease;
            margin-left: 8px;
          }

          &:hover {
            border-color: $primary-blue;

            .font-select-arrow {
              color: $primary-blue;
            }
          }
        }
      }
    }

    // Action Buttons Styles
    .action-buttons {
      display: flex;
      gap: 12px;
      justify-content: center;
      padding-top: 24px;

      .update-button {
        background: $primary-blue !important;
        border-color: $primary-blue !important;
        color: white !important;
        border-radius: 6px;
        padding: 8px 24px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.5;
        height: auto;
        min-width: 100px;

        &:hover {
          background: darken($primary-blue, 10%) !important;
          border-color: darken($primary-blue, 10%) !important;
        }
      }

      .cancel-button,
      .load-preview-button {
        background: white !important;
        border: 1px solid $border-color !important;
        color: $text-dark !important;
        border-radius: 6px;
        padding: 8px 24px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.5;
        height: auto;
        min-width: 100px;

        &:hover {
          border-color: $primary-blue !important;
          color: $primary-blue !important;
        }
      }
    }
  }

  // Font dropdown styling
  .font-dropdown {
    .ant-dropdown-menu {
      border-radius: 6px;
      border: 1px solid $border-color;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 4px;
      min-width: 200px;

      .ant-dropdown-menu-item {
        border-radius: 4px;
        padding: 0;
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }

        .font-option {
          padding: 8px 12px;
          font-size: 14px;
          color: $text-dark;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.2s ease;

          &.selected {
            background-color: $primary-blue;
            color: white;
            font-weight: 500;
          }

          &:not(.selected):hover {
            background-color: rgba(59, 96, 228, 0.1);
          }
        }

        &:hover {
          background: none;
        }
      }
    }
  }

  // Mobile responsive adjustments
  @media (max-width: 767px) {
    .typography {
      .setting-section {
        padding: 20px 0;
        flex-direction: column;
        
        .setting-info {
          margin-bottom: 16px;
          
          .setting-title {
            font-size: 15px;
          }
          
          .setting-description {
            font-size: 13px;
          }
        }

        .setting-control {
          justify-content: center;

          .font-select-trigger {
            width: 200px;
            max-width: 100%;
            padding: 8px 16px;

            .font-select-text {
              font-size: 14px;
            }
          }
        }
      }
      
      .action-buttons {
        flex-direction: column;
        gap: 8px;
        align-items: center;
        
        .update-button,
        .cancel-button,
        .load-preview-button {
          width: 200px;
          padding: 10px 24px;
          line-height: 1.4;
        }
      }
    }
  }
}   