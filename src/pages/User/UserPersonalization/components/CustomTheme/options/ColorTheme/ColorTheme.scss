// Import color variables from parent components
$primary-blue: #3B60E4;
$text-dark: #333;
$text-muted: #666;
$background-light: #f8f9fa;
$background-white: #fff;
$border-color: #e9ecef;
$sidebar-active-bg: #E6EBF6;
$success-green: #28a745;
$warning-yellow: #ffc107;
$danger-red: #dc3545;

.color-theme-wrapper {
  .color-theme {
    width: 100%;
    
    .setting-section {
      padding: 24px 0;
      border-bottom: 1px solid $border-color;
      display: flex;
      align-items: center;
      
      &:last-child {
        border-bottom: none;
      }
      
      .setting-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        
        .setting-title {
          color: $text-dark;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }
        
        .setting-description {
          color: $text-muted;
          font-size: 14px;
          line-height: 1.5;
          margin: 0;
        }
      }
    }

    // Theme Cards Section
    .theme-cards {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      margin-top: 16px;
      
      .theme-card {
        flex: 1;
        min-width: 200px;
        max-width: 300px;
        border: 2px solid $border-color;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: $background-white;
        
        &:hover {
          border-color: $primary-blue;
          box-shadow: 0 4px 12px rgba(59, 96, 228, 0.1);
        }
        
        &.active {
          border-color: $primary-blue;
          background: $sidebar-active-bg;
          box-shadow: 0 4px 12px rgba(59, 96, 228, 0.15);
        }
        
        .theme-preview {
          margin-bottom: 12px;

          // Common window controls for all previews
          .window-controls {
            position: absolute;
            top: 8px;
            left: 8px;
            display: flex;
            gap: 4px;
            z-index: 2;
            
            .control-btn {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              
              &.red { background: #ff5f56; }
              &.yellow { background: #ffbd2e; }
              &.green { background: #27ca3f; }
            }
          }
          
          // Light theme preview
          .preview-light-preview {
            width: 100%;
            height: 120px;
            border: 1px solid $border-color;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            background: #f5f5f5;
            
            .content-placeholders {
              width: 100%;
              height: 100%;
              padding: 24px 12px 12px;
              
              .header-bar {
                height: 12px;
                background: #e0e0e0;
                border-radius: 6px;
                margin-bottom: 8px;
              }
              
              .content-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 6px;
                
                .small-box,
                .large-box {
                  background: #e0e0e0;
                  border-radius: 4px;
                  height: 20px;
                }
                
                .large-box {
                  grid-column: span 2;
                }
              }
            }
          }
          
          // Dark theme preview
          .preview-dark-preview {
            @extend .preview-light-preview;
            background: #2c2c2c;
            
            .content-placeholders {
              .header-bar,
              .content-grid .small-box,
              .content-grid .large-box {
                background: #404040;
              }
            }
          }
          
          // System theme preview
          .preview-system {
            width: 100%;
            height: 120px;
            border: 1px solid $border-color;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(90deg, #f5f5f5 50%, #2c2c2c 50%);
            
            .system-content {
              width: 100%;
              height: 100%;
              padding: 24px 12px 12px;
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 6px;

              .system-box {
                height: 20px;
                border-radius: 4px;
              }

              // Left side boxes (dark boxes on light background)
              .left-large-1,
              .left-large-2 {
                grid-column: span 2;
                background: #404040;
              }
              
              .left-small-1,
              .left-small-2 {
                background: #404040;
              }

              // Right side boxes (light boxes on dark background)
              .right-large-1,
              .right-large-2 {
                grid-column: span 2;
                background: #e0e0e0;
              }
              
              .right-small-1,
              .right-small-2 {
                background: #e0e0e0;
              }
            }
          }
        }
        
        .theme-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px;
          border-radius: 6px;
          background: #f8f9fa;
          
          .theme-name {
            color: $text-dark;
            font-size: 14px;
            font-weight: 500;
          }
        }
        
        &.active {
          .theme-info {
            background: $sidebar-active-bg;
          }
        }
      }
    }
    
    // Action Buttons Styles
    .action-buttons {
      display: flex;
      gap: 12px;
      justify-content: center;
      padding-top: 24px;
      
      .update-button {
        background: $primary-blue !important;
        border-color: $primary-blue !important;
        color: white !important;
        border-radius: 6px;
        padding: 8px 24px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.5;
        height: auto;
        min-width: 100px;
        
        &:hover {
          background: darken($primary-blue, 10%) !important;
          border-color: darken($primary-blue, 10%) !important;
        }
      }
      
      .cancel-button,
      .load-preview-button {
        background: white !important;
        border: 1px solid $border-color !important;
        color: $text-dark !important;
        border-radius: 6px;
        padding: 8px 24px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.5;
        height: auto;
        min-width: 100px;
        
        &:hover {
          border-color: $primary-blue !important;
          color: $primary-blue !important;
        }
      }
    }
    
    // Mobile responsive adjustments
    @media (max-width: 767px) {
      .setting-section {
        padding: 20px 0;
        
        .setting-info {
          margin-bottom: 16px;
          
          .setting-title {
            font-size: 15px;
          }
          
          .setting-description {
            font-size: 13px;
          }
        }
      }
      
      .theme-cards {
        flex-direction: column;
        gap: 12px;
        
        .theme-card {
          min-width: 100%;
          max-width: none;
        }
      }
      
      .action-buttons {
        flex-direction: column;
        gap: 8px;
        align-items: center;
        
        .update-button,
        .cancel-button,
        .load-preview-button {
          width: 200px;
          padding: 10px 24px;
          line-height: 1.4;
        }
      }
    }
  }
} 