import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import { Button, Switch } from 'antd';
import { CustomInput } from '../../../CommonComponents';
import './WhiteLabelling.scss';

function WhiteLabelling() {
  const [enableWhiteLabel, setEnableWhiteLabel] = useState(true);
  const [replaceFooterLinks, setReplaceFooterLinks] = useState(false);
  const [customFooterText, setCustomFooterText] = useState('');
  const [disableDaakiaWatermark, setDisableDaakiaWatermark] = useState(true);

  return (
    <div className="white-labelling-wrapper">
      <div className="white-labelling">
        {/* Enable White Label Mode Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Enable white label mode</h3>
              <p className="setting-description">
                description
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <div className="toggle-control">
                <Switch
                  checked={enableWhiteLabel}
                  onChange={setEnableWhiteLabel}
                  className="custom-switch"
                />
                <span className="toggle-status">
                  {enableWhiteLabel ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </Col>
        </Row>

        {/* Replace Footer Links Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Replace footer links</h3>
              <p className="setting-description">
                Supports the primary color for elements like cards and carousels.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <div className="toggle-control">
                <Switch
                  checked={replaceFooterLinks}
                  onChange={setReplaceFooterLinks}
                  className="custom-switch"
                />
                <span className="toggle-status">
                  {replaceFooterLinks ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </Col>
        </Row>

        {/* Custom Footer Text Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Custom footer text</h3>
              <p className="setting-description">
                Highlights key elements like buttons and calls to action.
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <CustomInput
                type="text"
                label="Footer text"
                value={customFooterText}
                onChange={(e) => setCustomFooterText(e.target.value)}
                placeholder="Input Text"
                required
              />
            </div>
          </Col>
        </Row>

        {/* Disable Daakia Watermark Section */}
        <Row className="setting-section">
          <Col md={8} sm={12}>
            <div className="setting-info">
              <h3 className="setting-title">Disable Daakia watermark</h3>
              <p className="setting-description">
                Add a transparency layer to your side bar
              </p>
            </div>
          </Col>
          <Col md={4} sm={12}>
            <div className="setting-control">
              <div className="toggle-control">
                <Switch
                  checked={disableDaakiaWatermark}
                  onChange={setDisableDaakiaWatermark}
                  className="custom-switch"
                />
                <span className="toggle-status">
                  {disableDaakiaWatermark ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </Col>
        </Row>

        {/* Action Buttons */}
        <Row className="setting-section">
          <Col md={12}>
            <div className="action-buttons">
              <Button type="primary" className="update-button">
                Update
              </Button>
              <Button className="cancel-button">
                Cancel
              </Button>
              <Button className="load-preview-button">
                Load Preview
              </Button>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default WhiteLabelling;