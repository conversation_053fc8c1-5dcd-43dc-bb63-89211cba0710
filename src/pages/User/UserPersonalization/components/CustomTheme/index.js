import React, { useState } from 'react';
import './CustomTheme.scss';
import AppIdentity from './options/AppIdentity';
import ColorTheme from './options/ColorTheme';
import Typography from './options/Typography';
import WhiteLabelling from './options/WhiteLabelling';
import CustomDomain from './options/Customdomain';

function CustomTheme() {
  const [activeTab, setActiveTab] = useState('appIdentity');

  const tabs = [
    { id: 'appIdentity', label: 'App Identity' },
    { id: 'colorTheme', label: 'Color Theme' },
    { id: 'typography', label: 'Typography' },
    { id: 'whiteLabelling', label: 'White Labelling' },
    { id: 'customDomain', label: 'Custom Domain' }
  ];

  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);

  const handlePreviousTab = () => {
    if (currentTabIndex > 0) {
      setActiveTab(tabs[currentTabIndex - 1].id);
    }
  };

  const handleNextTab = () => {
    if (currentTabIndex < tabs.length - 1) {
      setActiveTab(tabs[currentTabIndex + 1].id);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'appIdentity':
        return <AppIdentity />;
      case 'colorTheme':
        return <ColorTheme />;
      case 'typography':
        return <Typography />;
      case 'whiteLabelling':
        return <WhiteLabelling />;
      case 'customDomain':
        return <CustomDomain />;
      default:
        return null;
    }
  };

  return (
    <div className="custom-theme-wrapper">
      <div className="settings-section">
        <div className="customTheme-section">
          {/* Tab Navigation */}
          <div className="tab-navigation">
            {/* Left Arrow */}
            <button
              className="nav-arrow-left"
              onClick={handlePreviousTab}
              disabled={currentTabIndex === 0}
            >
              ‹
            </button>

            {/* Right Arrow */}
            <button
              className="nav-arrow-right"
              onClick={handleNextTab}
              disabled={currentTabIndex === tabs.length - 1}
            >
              ›
            </button>

            {/* Tabs Container */}
            <div className="tabs-container">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`tab-item ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="tab-container">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
}

export default CustomTheme; 