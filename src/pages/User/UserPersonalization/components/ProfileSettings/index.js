import React from 'react';
import './ProfileSettings.scss';

function ProfileSettings() {
  return (
    <div className="profile-settings-wrapper">
      <div className="settings-section">
        <div className="form-section">
          <div className="form-group">
            <label>Full Name</label>
            <input type="text" className="form-control" placeholder="Enter your full name" />
          </div>
          <div className="form-group">
            <label>Email Address</label>
            <input type="email" className="form-control" placeholder="Enter your email" />
          </div>
          <div className="form-group">
            <label>Phone Number</label>
            <input type="tel" className="form-control" placeholder="Enter your phone number" />
          </div>
          <div className="form-group">
            <label>Date of Birth</label>
            <input type="date" className="form-control" />
          </div>
          <div className="form-group">
            <label>Bio</label>
            <textarea className="form-control" rows="4" placeholder="Tell us about yourself" />
          </div>
          <div className="form-actions">
            <button className="btn btn-primary">Save Changes</button>
            <button className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileSettings; 