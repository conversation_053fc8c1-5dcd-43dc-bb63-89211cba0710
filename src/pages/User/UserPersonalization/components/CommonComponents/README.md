# CustomInput Component

A reusable input component that supports multiple input types with consistent styling.

## Usage

```jsx
import { CustomInput } from './CommonComponents';

// Text Input
<CustomInput
  type="text"
  label="Brand name"
  value={brandName}
  onChange={handleChange}
  placeholder="Enter brand name"
  required={true}
  maxLength={60}
/>

// Color Picker
<CustomInput
  type="color"
  label="Custom color"
  colorValue={color}
  onColorChange={handleColorChange}
  placeholder="#9E9E9E"
/>

// Upload Area
<CustomInput
  type="upload"
  uploadProps={uploadProps}
  uploadIcon={uploadIcon}
  uploadInfo="Supports PNG, JPG, GIF, SVG (Max 5MB)"
/>

// Switch/Toggle
<CustomInput
  type="switch"
  switchValue={isEnabled}
  onSwitchChange={setIsEnabled}
  switchLabel={isEnabled ? 'Enabled' : 'Disabled'}
/>

// Button
<CustomInput
  type="button"
  buttonText="Coming soon"
  buttonDisabled={true}
/>
```

## Props

### Common Props
- `type`: Input type ('text', 'email', 'password', 'url', 'color', 'upload', 'switch', 'button')
- `label`: Label text for the input
- `required`: Whether the field is required (shows red asterisk)
- `error`: Error message to display
- `disabled`: Whether the input is disabled
- `className`: Additional CSS classes
- `customStyle`: Inline styles object

### Text Input Props
- `value`: Input value
- `onChange`: Change handler function
- `placeholder`: Placeholder text
- `maxLength`: Maximum character length

### Color Picker Props
- `colorValue`: Current color value
- `onColorChange`: Color change handler

### Upload Props
- `uploadProps`: Upload configuration object
- `uploadIcon`: Icon for upload area
- `uploadInfo`: Information text for upload

### Switch Props
- `switchValue`: Current switch state
- `onSwitchChange`: Switch change handler
- `switchLabel`: Custom label for switch

### Button Props
- `buttonText`: Button text
- `buttonType`: Button type ('default', 'primary', etc.)
- `onButtonClick`: Click handler
- `buttonDisabled`: Whether button is disabled

## Features

✅ **Multiple Input Types**: Text, color, upload, switch, button  
✅ **Consistent Styling**: All inputs follow the same design pattern  
✅ **Error Handling**: Built-in error display  
✅ **Validation**: Required field indicators  
✅ **Responsive**: Mobile-friendly design  
✅ **Accessible**: Proper ARIA labels and focus states  
✅ **Customizable**: Extensive prop options for customization  

