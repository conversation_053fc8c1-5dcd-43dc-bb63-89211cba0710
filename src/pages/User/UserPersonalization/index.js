import React, { useState } from "react";
import { Container, Row, Col } from "react-bootstrap";
import { BgColorsOutlined } from "@ant-design/icons";
import {
  CustomTheme
} from "./components";
import "./UserPersonalization.scss";

function UserPersonalization() {
  const [activeSection, setActiveSection] = useState('customTheme');

  const navigationItems = [
    { id: 'customTheme', label: 'Personalized Branding', icon: <BgColorsOutlined /> }
  ];

  const renderContent = () => {
    return <CustomTheme />;
  };

  const getSectionTitle = () => {
    const currentItem = navigationItems.find(item => item.id === activeSection);
    return currentItem ? currentItem.label : 'Personalized Branding';
  };

  const getSectionDescription = () => {
    return 'Create a consistent, branded login experience by yourself';
  };

  return (
    <div className="user-personalization-wrapper">
      <div className="mainContent">
        <div className="user-personalization">
          {/* Header Section */}
          <div className="personalization-header">
            <h1>User Personalization</h1>
          </div>

          {/* Main Layout with Sidebar and Content */}
          <div className="personalization-layout">
            <Container fluid>
              <Row className="h-100">
                {/* Left Sidebar */}
                <Col lg={2} md={3} className="personalization-sidebar">
                  <div className="sidebar-content">
                    <nav className="sidebar-nav">
                      <ul className="nav-list">
                        {navigationItems.map((item) => (
                          <li 
                            key={item.id}
                            className={`nav-item ${activeSection === item.id ? 'active' : ''}`}
                          >
                            <button
                              className="nav-link"
                              onClick={() => setActiveSection(item.id)}
                            >
                              <span className="nav-icon">
                                {item.icon}
                              </span>
                              {item.label}
                            </button>
                          </li>
                        ))}
                      </ul>
                    </nav>
                  </div>
                </Col>

                {/* Main Content Area */}
                <Col lg={10} md={9} className="personalization-main">
                  <div className="main-content">
                    <div className="content-header">
                      <div className="header-content">
                        <h2 className="section-title">{getSectionTitle()}</h2>
                        <span className="section-description">{getSectionDescription()}</span>
                      </div>
                    </div>

                    <div className="content-body">
                      {renderContent()}
                    </div>
                  </div>
                </Col>
              </Row>
            </Container>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserPersonalization;