// Color variables - only used colors
$primary-blue: #3B60E4;
$text-dark: #333;
$text-muted: #666;
$background-light: #f8f9fa;
$background-white: #fff;
$border-color: #e9ecef;
$sidebar-active-bg: #E6EBF6;

.user-personalization-wrapper {
  .user-personalization {
    min-height: fit-content;

    .personalization-header {
      background: $background-white;
      padding: 14px 0;
      border-bottom: 1px solid $border-color;
      margin-bottom: 0;

      h1 {
        margin: 0;
        color: $text-dark;
        font-size: 22px;
        font-weight: 600;
        padding-left: 24px;
      }
    }

    .personalization-layout {
      padding: 0;

      .personalization-sidebar {
        background: $background-white;
        border-right: 1px solid $border-color;
        padding: 0;

        .sidebar-content {
          padding: 10px 16px;

          .sidebar-nav {
            .nav-list {
              list-style: none;
              margin: 0;
              padding: 0;

              .nav-item {
                margin-bottom: 4px;

                .nav-link {
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                  width: 100%;
                  padding: 12px 16px;
                  background: transparent;
                  border: none;
                  text-decoration: none;
                  color: $text-muted;
                  font-size: 14px;
                  font-weight: 500;
                  transition: all 0.3s ease;
                  cursor: pointer;
                  border-radius: 8px;
                  text-align: left;

                  &:hover {
                    background: $sidebar-active-bg;
                    color: $text-dark;
                  }

                  &:focus {
                    outline: none;
                    box-shadow: none;
                  }

                  .nav-icon {
                    margin-right: 12px;
                    font-size: 16px;
                    width: 20px;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                  }
                }

                &.active {
                  .nav-link {
                    background: $sidebar-active-bg;
                    color: $primary-blue;
                    font-weight: 600;

                    &:hover {
                      background: $sidebar-active-bg;
                      color: $primary-blue;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .personalization-main {
        background: $background-white;
        padding: 0;

        .main-content {
          .content-header {
            padding: 20px 16px;
            border-bottom: 1px solid $border-color;

            .header-content {
              display: flex;
              align-items: baseline;
              gap: 12px;

              .section-title {
                color: $primary-blue;
                font-size: 20px;
                font-weight: 600;
                margin: 0;
              }

              .section-description {
                color: $text-muted;
                font-size: 14px;
                margin: 0;
                line-height: 1;
              }
            }
          }

          // .content-body {
          //   // Content will be rendered by individual components
          // }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .user-personalization {
      .personalization-header {
        display: none; // Hide header on mobile
      }

      .personalization-layout {
        min-height: 100vh; // Full height since header is hidden
        
        .personalization-sidebar {
          border-right: none;
          border-bottom: 1px solid $border-color;

          .sidebar-content {
            padding: 16px;

            .sidebar-nav {
              .nav-list {
                display: flex;
                overflow-x: auto;
                gap: 8px;
                padding-bottom: 10px;

                .nav-item {
                  margin-bottom: 0;
                  flex-shrink: 0;

                  .nav-link {
                    padding: 8px 16px;
                    white-space: nowrap;
                    border-radius: 20px;
                    font-size: 13px;
                    justify-content: flex-start;
                    text-align: left;

                    .nav-icon {
                      margin-right: 6px;
                      font-size: 14px;
                      flex-shrink: 0;
                    }

                    &:hover {
                      background: $sidebar-active-bg;
                      color: $text-dark;
                    }
                  }

                  &.active {
                    .nav-link {
                      background: $sidebar-active-bg;
                      color: $primary-blue;

                      &:hover {
                        background: $sidebar-active-bg;
                        color: $primary-blue;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .personalization-main {
          .main-content {
            .content-header {
              padding: 16px;

              .header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .section-title {
                  font-size: 18px;
                }

                .section-description {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .user-personalization {
      .personalization-header {
        display: none; // Hide header on mobile
      }

      .personalization-layout {
        min-height: 100vh; // Full height since header is hidden
        
        .personalization-main {
          .main-content {
            .content-header {
              padding: 12px;

              .header-content {
                .section-title {
                  font-size: 16px;
                }

                .section-description {
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }
  }
}
