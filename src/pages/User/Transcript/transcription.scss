// $primary-color: #4a90e2;
// $secondary-color: #ffffff;
// $text-color: #333;
// $border-color: #ddd;
// $box-background: #f4f8f9;
// $inner-box-background: white;
// $tab-background: #e0e0e0;
// $tab-active-background: white;

// .mainBody {
//   height: 100%;
// }
// .mainContent {
//   height: 100%;
//   position: relative;
// }
// .main {
//   display: flex;
//   padding: 20px 30px;
//   font-family: "Inter";
//   background-color: $box-background;
//   height: 100%;
//   &-no-subscription {
//     padding-bottom: 20px;
//     padding-top: 20px;
//     height: 92svh;
//     .main-left {
//       margin: 0;
//       width: 100%;
//       .video-section {
//         width: 100% !important;
//         height: 100% !important;
//         .video-thumbnail {
//           width: 100%;
//           height: 100%;
//           object-fit: cover;
//         }
//       }
//     }
//   }
//   &-successed {
//     height: 100%;
//     // max-height: calc(100svh - 4rem);
//   }
//   &-not {
//     &-plan {
//       margin: 0 !important;
//       padding: 20px 0;
//       flex-direction: column;
//       .video-section {
//         margin: 0 !important;
//         width: 100% !important;
//       }
//     }
//     &-successed {
//       width: 100% !important;
//       height: -webkit-fill-available;
//       padding: 0 20px;
//       margin-right: 0 !important;
//       position: relative;
//       flex-direction: row !important;
//       .main-left {
//         padding: 20px 0;
//         margin: 0;
//       }
//       .video-section {
//         margin: 0 !important;
//         height: 100% !important;
//         max-height: 100% !important;
//       }
//     }
//   }
//   &-no-subscription {
//     // width: 100% !important;
//     // margin-right: 0 !important;
//     // position: relative;
//     .video-section {
//       // height: 89svh !important;
//       // width: 100% !important;
//       // margin: 0 !important;
//       .video-thumbnail {
//         // height: 100%;
//         // width: 100% !important;
//       }
//     }
//   }
//   &-left {
//     width: 75%;
//     display: flex;
//     gap: 1rem;
//     position: relative;
//     &-shared{
//       .main-left-inner{
//         .video-shared{
//           height: 100%;
//         }
//       }
//     }
//     &-inner{
//       width: 98%;
//       height: 100%;
//       display: flex;
//       flex-direction: column;
//       justify-content: space-around;
//       align-items: center;
//       gap: 1rem;
//       position: relative;
//     }
//     &-failed{
//       width: 100%;
//       flex-direction: row;
//       .main-left-inner{
//         .video-section{
//           height: 100% !important;
//           width: 100% !important;
//         }
//       }
//       .video-failed{
//         width: 100%;
//         height: 100%;
//         object-fit: cover;
//       }
//     }
//     .more-items {
//       width: 100%;
//       .ant-tabs {
//         &-nav {
//           margin-bottom: 0.3rem;
//         }
//         &-tab-btn {
//           span {
//             font-size: 16px;
//             font-weight: 600;
//           }
//         }
//         &-tabpane {
//           width: 100%;
//           background-color: #fff;
//           border: 1px solid #3b60e4;
//           border-radius: 10px;
//           height: 12rem;
//           max-height: 12rem;
//           .action-items-content,
//           .key-insights-content {
//             padding: 1rem;
//             padding-right: 0;
//             padding-bottom: 0;
//             ul {
//               padding-left: 1rem;
//               padding-right: 1rem;
//               max-height: 10rem;
//               overflow-y: auto;
//               &::-webkit-scrollbar {
//                 width: 0.5rem;
//               }
//               &::-webkit-scrollbar-thumb {
//                 background-color: #cecece;
//                 border-radius: 10px;
//               }
//               &::-webkit-scrollbar-track {
//                 background-color: transparent;
//               }
//               li {
//                 display: flex;
//                 gap: 0.5rem;
//                 justify-content: space-between;
//                 span {
//                   font-size: 14px;
//                   font-weight: 400;
//                   &:nth-child(1) {
//                     width: 86%;
//                     &::before {
//                       content: "●";
//                       font-size: 14px;
//                       margin-right: 0.5rem;
//                     }
//                   }
//                   &:nth-child(2) {
//                     font-size: 12px;
//                     font-weight: 600;
//                   }
//                 }
//               }
//             }
//           }
//           .summary-content {
//             height: 100%;
//             padding: 1rem;
//             padding-right: 0;
//             p {
//               height: 100%;
//               overflow-y: auto;
//               padding-right: 1rem;
//               &::-webkit-scrollbar {
//                 width: 0.5rem;
//               }
//               &::-webkit-scrollbar-thumb {
//                 background-color: #cecece;
//                 border-radius: 10px;
//               }
//               &::-webkit-scrollbar-track {
//                 background-color: transparent;
//               }
//             }
//           }
//         }
//       }
//     }
//     .header {
//       display: flex;
//       justify-content: flex-start;
//       gap: 1rem;
//       &-details {
//         display: flex;
//         background-color: #dedede;
//         border-radius: 15px;
//         padding: 4px 8px;
//         color: #555555;
//         align-items: center;
//         gap: 0.5rem;
//         span {
//           font-size: 13px;
//           font-weight: 400;
//         }
//         &:nth-child(2) {
//           position: relative;
//           width: 10.5rem;
//           justify-content: flex-end;
//           round {
//             position: absolute;
//             left: 8px;
//             &:nth-child(2) {
//               left: 24px;
//               background-color: blue;
//             }
//             &:nth-child(3) {
//               left: 40px;
//               background-color: green;
//             }
//           }
//         }
//         round {
//           background-color: red;
//           border-radius: 50%;
//           width: 20px;
//           height: 20px;
//           color: #555555;
//         }
//       }
//     }
//     .video {
//       &-no-subscription {
//         img {
//           width: 100% !important;
//         }
//         .video-details {
//           &-title {
//             font-size: 18px;
//           }
//           &-date {
//             font-size: 14px;
//           }
//         }
//       }
//       &-shared {
//         width: 100% !important;
//         height: 100%;
//         display: flex;
//         justify-content: center;
//         align-items: center;
//       }
//       &-failed{
//         height: 100% !important;
//         width: 100% !important;
//       }
//       &-section {
//         width: 75%;
//         height: 60%;
//         display: flex;
//         flex-direction: column;
//         gap: 1rem;
//         // margin-left: 10px;
//         border-radius: 20px;
//         overflow: hidden;
//         background-color: #000;
//         box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.4);
//         img {
//           width: 30rem;
//         }
//       }
//       &-details {
//         display: flex;
//         flex-direction: column;
//         &-title {
//           display: flex;
//           align-items: center;
//           gap: 0.5rem;
//           font-size: 14px;
//           font-weight: 600;
//           &-icon {
//             font-size: 18px;
//             color: #3b60e4;
//           }
//         }
//         &-date {
//           font-size: 11px;
//           color: #4b4b4b;
//         }
//       }
//     }
//     .insights {
//       &-action {
//         border: 1px solid #3b60e4;
//         padding: 1rem;
//         background-color: #fff;
//         border-radius: 15px;
//         padding-right: 0;
//         h3 {
//           color: #555555;
//           font-size: 16px;
//           font-weight: 600;
//           margin: 0.5rem 0 1rem 0;
//         }
//         &-list {
//           display: flex;
//           flex-direction: column;
//           color: #555555;
//           font-size: 16px;
//           padding-left: 0.5rem;
//           gap: 0.5rem;
//           margin: 0;
//           max-height: 15rem;
//           overflow-y: auto;
//           // margin-right: 1rem;
//           &::-webkit-scrollbar {
//             width: 0.5rem;
//           }
//           &::-webkit-scrollbar-thumb {
//             background-color: #cecece;
//             border-radius: 10px;
//           }
//           &::-webkit-scrollbar-track {
//             background-color: transparent;
//           }
//           li {
//             display: flex;
//             gap: 1rem;
//             &::before {
//               content: "●";
//               font-size: 14px;
//               margin-right: 0.5rem;
//             }
//           }
//         }
//       }
//       &-summary {
//         border: 1px solid #3b60e4;
//         padding: 1rem;
//         background-color: #fff;
//         border-radius: 15px;
//         padding-right: 0;
//         h3 {
//           color: #555555;
//           font-size: 16px;
//           font-weight: 600;
//           margin: 0.5rem 0 1rem 0;
//         }
//         p {
//           max-height: 15rem;
//           overflow-y: auto;
//           padding-right: 1rem;
//           margin: 0;
//           &::-webkit-scrollbar {
//             width: 0.5rem;
//           }
//           &::-webkit-scrollbar-thumb {
//             background-color: #cecece;
//             border-radius: 10px;
//           }
//           &::-webkit-scrollbar-track {
//             background-color: transparent;
//           }
//         }
//       }
//       &-key {
//         display: flex;
//         flex-direction: column;
//         border: 1px solid #3b60e4;
//         padding: 1rem;
//         background-color: #fff;
//         border-radius: 15px;
//         padding-right: 0;
//         gap: 1rem;
//         h3 {
//           color: #555555;
//           font-size: 16px;
//           font-weight: 600;
//           margin: 0.5rem 0 1rem 0;
//         }
//         &-list {
//           display: flex;
//           flex-direction: column;
//           color: #555555;
//           font-size: 16px;
//           padding-left: 0.5rem;
//           gap: 0.5rem;
//           margin: 0;
//           max-height: 15rem;
//           overflow-y: auto;
//           &::-webkit-scrollbar {
//             width: 0.5rem;
//           }
//           &::-webkit-scrollbar-thumb {
//             background-color: #cecece;
//             border-radius: 10px;
//           }
//           &::-webkit-scrollbar-track {
//             background-color: transparent;
//           }
//           li {
//             display: flex;
//             justify-content: space-between;
//             gap: 1rem;
//             &::before {
//               content: "●";
//               font-size: 14px;
//               margin-right: 0.5rem;
//             }
//             span {
//               &:nth-child(1) {
//                 width: 76%;
//               }
//               &:nth-child(2) {
//                 min-width: 7.5rem;
//                 text-align: right;
//                 margin-right: 1rem;
//               }
//             }
//           }
//         }
//       }
//     }
//   }
//   &-right {
//     width: 25%;
//     border-left: 1px solid #bebebe;
//     display: flex;
//     justify-content: flex-end;
//     gap: 1rem;
//     &-inner {
//       width: 93.5%;
//       .transcription-tabs {
//         width: 100%;
//         height: 100%;
//         position: relative;
//         &-shared{
//           .ant-tabs-tab-disabled{
//             display: none;
//           }
//         }
//         .ant-tabs {
//           &-nav {
//             margin-bottom: 0;
//           }
//           &-content {
//             height: 100%;
//           }
//           &-tabpane {
//             height: 100%;
//             position: relative;
//           }
//         }
//         .chat {
//           &-input {
//             display: flex;
//             align-items: center;
//             justify-content: space-between;
//             &-send {
//               background-color: #3b60e4;
//               color: white;
//               font-size: 18px;
//               display: flex;
//               justify-content: center;
//               align-items: center;
//               width: 30px;
//               height: 30px;
//               padding: 7px;
//               border-radius: 50%;
//             }
//           }
//           &-ai {
//             display: flex;
//             flex-direction: column;
//             justify-content: flex-end;
//             background-color: #fff;
//             border: 1px solid #3b60e4;
//             width: 100%;
//             padding: 1rem;
//             border-radius: 10px;
//             height: 100%;
//           }
//           &-content {
//             display: flex;
//             flex-direction: column;
//             gap: 0.5rem;
//             &-message {
//               display: flex;
//               gap: 0.5rem;
//             }
//           }
//         }
//         .participants {
//           &-content {
//             background-color: #fff;
//             border: 1px solid #3b60e4;
//             border-radius: 10px;
//             height: 100%;
//             position: relative;
//             overflow: hidden;
//             ul {
//               padding-left: 15px;
//             }
//           }
//           &-header {
//             display: flex;
//             align-items: center;
//             height: 3rem;
//             border-bottom: 1px solid #3b60e4;
//             padding: 0.7rem;
//           }
//           &-list {
//             display: flex;
//             flex-direction: column;
//             padding: 0.7rem;
//             gap: 0.5rem;
//             &-outer {
//               display: flex;
//               flex-direction: column;
//               gap: 0.5rem;
//               height: 100%;
//               overflow: auto;
//               &::-webkit-scrollbar {
//                 width: 0.5rem;
//               }
//               &::-webkit-scrollbar-thumb {
//                 background-color: #cecece;
//                 border-radius: 10px;
//               }
//               &::-webkit-scrollbar-track {
//                 background-color: transparent;
//               }
//             }
//             &-item {
//               display: flex;
//               gap: 0.5rem;
//               align-items: center;
//               justify-content: space-between;
//               &-details {
//                 display: flex;
//                 flex-direction: column;
//                 width: 85%;
//                 &-name {
//                   font-size: 14px;
//                   font-weight: 600;
//                 }
//                 &-role {
//                   &-time {
//                     display: flex;
//                     align-items: center;
//                     justify-content: space-between;
//                   }
//                   font-size: 12px;
//                 }
//                 &-time {
//                   font-size: 12px;
//                 }
//               }
//             }
//           }
//         }
//         .notes {
//           &-content {
//             background-color: #fff;
//             border: 1px solid #3b60e4;
//             padding: 0.7rem;
//             border-radius: 10px;
//             height: 100%;
//             ul {
//               padding-left: 15px;
//             }
//           }
//         }
//         .transcript {
//           &-content {
//             background-color: #fff;
//             border: 1px solid #3b60e4;
//             // padding: 0.7rem;
//             border-radius: 10px;
//             height: 100%;
//             max-height: 100%;
//             overflow: hidden;
//             padding: 0;
//             &-download {
//               display: flex;
//               align-items: center;
//               gap: 0.5rem;
//               font-size: 14px;
//               font-weight: 600;
//               cursor: pointer;
//               padding: 10px;
//               width: max-content;
//             }
//             .loading-container {
//               display: flex;
//               justify-content: center;
//               align-items: center;
//               height: 100%;
//               width: 100%;
//             }
//             div {
//               padding: 10px;
//               max-height: 100%;
//               overflow: auto;
//               p {
//                 margin: 0;
//               }
//               &::-webkit-scrollbar {
//                 width: 0.5rem;
//               }
//               &::-webkit-scrollbar-thumb {
//                 background-color: #cecece;
//                 border-radius: 10px;
//               }
//               &::-webkit-scrollbar-track {
//                 background-color: transparent;
//               }
//             }
//             &-caption {
//               display: flex;
//               flex-direction: column;
//               &-time {
//                 padding: 0 10px;
//                 color: #6a6969;
//                 font-size: 12px;
//                 font-weight: 500;
//               }
//               &-details {
//                 color: #000;
//                 font-size: 14px;
//                 span {
//                   font-weight: 600;
//                 }
//               }
//             }
//           }
//         }
//         .ant-tabs-nav {
//           &::before {
//             border: none;
//           }
//           .ant-tabs-ink-bar {
//             display: none;
//           }
//           .ant-tabs-tab-active {
//             .transcript-tab {
//               color: #3b60e4 !important;
//             }
//           }
//           .transcript {
//             &-tab {
//               display: flex;
//               align-items: center;
//               gap: 5px;
//             }
//           }
//         }
//       }
//     }
//     .ask-ai {
//       margin-top: 1rem;
//       width: 75px;
//       height: 75px;
//       background: linear-gradient(
//         180deg,
//         #ced9ff 0%,
//         #a5b7f8 50%,
//         #3b60e4 100%
//       );
//       border-radius: 50%;
//       display: flex;
//       justify-content: center;
//       align-items: center;
//       color: white;
//       position: fixed;
//       bottom: 1rem;
//       right: 1rem;
//       z-index: 1000;
//       cursor: pointer;
//       user-select: none;
//       display: none;
//       &-icon {
//         width: 35px;
//         height: auto;
//         margin-bottom: 0.5rem;
//       }
//       span {
//         position: absolute;
//         font-size: 9px;
//         bottom: 0.8rem;
//       }
//     }
//   }
// }

// .transcript-content-caption {
//   transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
//   padding: 12px;
//   border-radius: 8px;
//   margin: 4px 0;
//   position: relative;
//   z-index: 1;

//   &.active-transcript {
//     background-color: rgba(0, 0, 0, 0.05);
//     border-left: 3px solid #1890ff;
//     transform: scale(1.02);
//     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//     z-index: 2;
//   }

//   &:hover {
//     background-color: rgba(0, 0, 0, 0.02);
//   }
// }

// .transcript-content {
//   // padding: 16px;
//   max-height: calc(100vh - 300px);
//   overflow-y: auto;

//   &::-webkit-scrollbar {
//     width: 6px;
//   }

//   &::-webkit-scrollbar-track {
//     background: transparent;
//     border-radius: 3px;
//   }

//   &::-webkit-scrollbar-thumb {
//     background: #888;
//     border-radius: 3px;

//     &:hover {
//       background: #555;
//     }
//   }
// }

// // .video {
// //   &-shared{
// //     width: 100% !important;
// //     height: 95% !important;
// //   }
// //   &-section {
// //     width: inherit;
// //     height: 400px;
// //     position: relative;
// //     background: #000;
// //     border-radius: 8px;
// //     overflow: hidden;

// //     &.video-no-subscription {
// //       height: 300px;
// //     }

// //     .video-thumbnail {
// //       position: absolute !important;
// //       top: 0;
// //       left: 0;
// //       // width: 50rem !important;
// //       height: 100% !important;
// //       object-fit: contain;
// //     }
// //   }
// // }
// .transcription-checkbox {
//   .ant-checkbox-inner {
//     border-color: #3b60e4 !important;
//     border-width: 2px !important;
//   }
//   .ant-checkbox-checked {
//     .ant-checkbox-inner {
//       background-color: #3b60e4 !important;
//       display: flex !important;
//       justify-content: center !important;
//       align-items: center !important;
//       overflow: hidden;
//       &::after {
//         border-color: #fff !important;
//         top: 0 !important;
//         left: 2px !important;
//         border-width: 2px !important;
//       }
//     }
//   }
// }
