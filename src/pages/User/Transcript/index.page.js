/* eslint-disable */

import React, { useState, useEffect, useRef } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useParams, useSearchParams } from "react-router-dom";
import { decoder } from "../../../utils";
import { VideoConferenceService } from "../../../services/User/VideoConference/index.service";
import VideoPlayer from "./components/VideoPlayer";
import TranscriptCard from "./components/TranscriptCard";
import ParticipantCard from "./components/ParticipantCard";
import Sidebar from "./components/Sidebar";
import TabHeader from "./components/TabHeader";
import "./index.page.scss";
import moment from "moment";
import StatusCard, { EmptyStateCard } from "./components/StatusCard";
import ErrorCard from './components/ErrorCard';
import EmptyStateTest from './components/EmptyStateTest';
import { Container } from "react-bootstrap";

const formatTime = (seconds) => {
  if (!seconds) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.round(seconds % 60);

  const timeString = `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;

  return hours > 0
    ? `${String(hours).padStart(2, "0")}:${timeString}`
    : timeString;
};

const LoadingSpinner = () => (
  <div className="loading-container">
    <div className="loading-spinner"></div>
  </div>
);

function TranscriptionPage() {
  const [activeTab, setActiveTab] = useState('1');
  const [sidebarTab, setSidebarTab] = useState('1');
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showTest, setShowTest] = useState(false);
  const [forceEmptyState, setForceEmptyState] = useState(false);
  const [transcriptionData, setTranscriptionData] = useState(null);
  const [participantsData, setParticipantsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [transcriptionStatus, setTranscriptionStatus] = useState(null);
  const [activeMeetingAnalysisPlan, setActiveMeetingAnalysisPlan] = useState(null);
  const [isShared, setIsShared] = useState(false);
  const [tabsContentHeight, setTabsContentHeight] = useState('calc(100vh - 500px)');
  const isMounted = useRef(true);
  const apiCallInProgress = useRef(false);

  const { meetingId } = useParams();
  const [searchParams] = useSearchParams();
  const recordingId = decoder(searchParams.get("recordingId"));
  const sessionId = decoder(searchParams.get("sessionId"));
  const videoPlayerRef = useRef(null);

  const ErrorMessage = ({ type }) => {
    const error = errors.find(err => err.type === type);

    const getErrorIcon = (type) => {
      switch (type) {
        case 'key_insights':
          return '💡';
        case 'action_items':
          return '📋';
        case 'summary':
          return '📝';
        case 'participants':
          return '👥';
        default:
          return '⚠️';
      }
    };

    const getErrorMessage = (type) => {
      switch (type) {
        case 'key_insights':
          return {
            title: 'Key Insights Unavailable',
            description: 'We\'re having trouble loading the key insights at the moment.',
            action: 'Please try refreshing the page or contact support if the issue persists.'
          };
        case 'action_items':
          return {
            title: 'Action Items Unavailable',
            description: 'We\'re having trouble loading the action items at the moment.',
            action: 'Please try refreshing the page or contact support if the issue persists.'
          };
        case 'summary':
          return {
            title: 'Summary Unavailable',
            description: 'We\'re having trouble loading the summary at the moment.',
            action: 'Please try refreshing the page or contact support if the issue persists.'
          };
        case 'participants':
          return {
            title: 'Participants Unavailable',
            description: 'We\'re having trouble loading the participants information.',
            action: 'You can continue viewing other sections while we resolve this.'
          };
        default:
          return {
            title: 'Something Went Wrong',
            description: 'We encountered an unexpected issue.',
            action: 'Please try refreshing the page.'
          };
      }
    };

    if (!error) return null;

    const { title, description, action } = getErrorMessage(type);
    const icon = getErrorIcon(type);

    return (
      <div className="error-container" style={{
        padding: '20px',
        margin: '10px 0',
        borderRadius: '8px',
        backgroundColor: '#fff3f3',
        border: '1px solid #ffcdd2',
        color: '#d32f2f'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px'
        }}>
          <div style={{
            fontSize: '24px',
            lineHeight: '1'
          }}>
            {icon}
          </div>
          <div style={{
            flex: 1
          }}>
            <h4 style={{
              margin: '0 0 8px 0',
              fontSize: '16px',
              fontWeight: '600'
            }}>
              {title}
            </h4>
            <p style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              color: '#666'
            }}>
              {description}
            </p>
            <p style={{
              margin: '0',
              fontSize: '14px',
              color: '#666',
              fontStyle: 'italic'
            }}>
              {action}
            </p>
          </div>
        </div>
      </div>
    );
  };

  const addError = (type, message) => {
    console.error(`Error in ${type}:`, message);

    setErrors(prev => {
      const filteredErrors = prev.filter(error => error.type !== type);
      return [...filteredErrors, { type, timestamp: new Date().toISOString() }];
    });
  };

  const clearErrors = (type) => {
    if (type) {
      setErrors(prev => prev.filter(error => error.type !== type));
    } else {
      setErrors([]);
    }
  };



  const fetchParticipantsData = async () => {
    try {
      const participantsResponse = await VideoConferenceService.getParticipantsLogsService(
        decoder(meetingId),
        sessionId
      );

      if (!isMounted.current) return;

      if (participantsResponse.success) {
        setParticipantsData(participantsResponse.data);
        clearErrors('participants');
      } else {
        addError('participants', 'Failed to load participants data');
      }
    } catch (err) {
      console.error('Error fetching participants data:', err);
      if (isMounted.current) {
        addError('participants', err.message);
      }
    }
  };

  const fetchTranscriptionData = async (isRetry = false) => {
    if (apiCallInProgress.current && !isRetry) {
      return;
    }

    apiCallInProgress.current = true;
    setLoading(true);

    try {
      const transcriptionResponse = await VideoConferenceService.getTranscriptionDataService(
        decoder(meetingId),
        recordingId
      );

      if (!isMounted.current) return;

      if (transcriptionResponse.success === 1) {
        setTranscriptionData(transcriptionResponse.data);
        setTranscriptionStatus(transcriptionResponse.data?.status);
        setIsShared(transcriptionResponse.data?.is_share_user === false);
        setActiveMeetingAnalysisPlan(
          transcriptionResponse.data?.active_meeting_analysis_plan === 1 ? "ACTIVE" : "INACTIVE"
        );
        clearErrors();
      } else {
        // Set specific errors for each section
        if (!transcriptionResponse.data?.takeaways) {
          addError('key_insights', 'Failed to load key insights');
        }
        if (!transcriptionResponse.data?.summary) {
          addError('summary', 'Failed to load summary');
        }
        if (!transcriptionResponse.data?.action_items) {
          addError('action_items', 'Failed to load action items');
        }
      }
    } catch (err) {
      console.error('Error fetching transcription data:', err);
      if (isMounted.current) {
        addError('key_insights', err.message);
        addError('summary', err.message);
        addError('action_items', err.message);
      }
    } finally {
      if (isMounted.current) {
        apiCallInProgress.current = false;
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    isMounted.current = true;
    fetchTranscriptionData();
    fetchParticipantsData();

    return () => {
      isMounted.current = false;
    };
  }, [meetingId, recordingId]);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
      // Calculate the height for tabs content
      const videoPlayerHeight = 300; // Approximate height of video player
      const tabHeaderHeight = 50; // Approximate height of tab header
      const paddingAndMargins = 50; // Approximate height for padding and margins
      const calculatedHeight = window.innerHeight - (videoPlayerHeight + tabHeaderHeight + paddingAndMargins);
      setTabsContentHeight(`${calculatedHeight}px`);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const handleDownload = () => {
    if (sidebarTab === '1') {
      // Download transcript as CSV
      if (!transcriptionData?.transcription?.transcription) return;

      // Prepare CSV headers
      const headers = ['Time', 'Transcript'];

      // Prepare CSV data
      const csvData = transcriptionData.transcription.transcription.map((item) => {
        const cleanedTranscript = item?.transcript?.replace(/^,/, "") || "";

        return [
          formatTime(item?.start_time),
          cleanedTranscript
        ];
      });

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...csvData.map(row => row.map(field => `"${field}"`).join(','))
      ].join('\n');

      // Create and download the file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `transcript_${decoder(meetingId)}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (sidebarTab === '2') {
      // Download participants as Excel (CSV format)
      if (!participantsData?.rows) return;

      // Prepare CSV headers
      const headers = ['Name', 'Role', 'Join Time', 'Leave Time', 'Duration'];

      // Prepare CSV data
      const csvData = participantsData.rows.map((participant) => {
        const role = participant?.role === 'moderator' ? 'Host' : participant?.role;
        const joinTime = moment(participant?.joined_at);
        const leaveTime = moment(participant?.leave_at);
        const duration = moment.duration(leaveTime.diff(joinTime));
        const durationFormatted = `${Math.floor(duration.asHours())}:${String(duration.minutes()).padStart(2, '0')}:${String(duration.seconds()).padStart(2, '0')}`;

        return [
          participant?.screen_name || 'Unknown',
          role || 'Participant',
          joinTime.format("HH:mm"),
          leaveTime.format("HH:mm"),
          durationFormatted
        ];
      });

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...csvData.map(row => row.map(field => `"${field}"`).join(','))
      ].join('\n');

      // Create and download the file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `participants_${decoder(meetingId)}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleSeek = (time) => {
    if (videoPlayerRef.current) {
      videoPlayerRef.current.seekTo(time);
    }
  };

  const handleProgress = (progress) => {
    setCurrentTime(progress.playedSeconds);
  };

  // Helper function to check if data is empty
  const isDataEmpty = (data, type) => {
    if (!data) return true;

    if (type === 'key_insights' || type === 'action_items') {
      // Check if array exists and has valid items (not null/undefined)
      if (!Array.isArray(data)) return true;
      if (data.length === 0) return true;
      // Check if all items are null/undefined
      return data.every(item => !item || !item.text);
    }

    if (type === 'summary') {
      return !data || data.trim() === '';
    }

    if (type === 'transcription') {
      if (!Array.isArray(data)) return true;
      if (data.length === 0) return true;
      // Check if all items are null/undefined
      return data.every(item => !item || !item.transcript);
    }

    return !data;
  };

  const renderContent = (type, data, renderData) => {
    if (loading) {
      return <LoadingSpinner />;
    }

    if (errors.find(err => err.type === type)) {
      return <ErrorCard type={type} />;
    }

    // Force empty state for testing
    if (forceEmptyState) {
      return (
        <div className="d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <EmptyStateCard type={type} />
        </div>
      );
    }

    // If status is "not_available" or "not available", show status card everywhere regardless of data
    if (transcriptionStatus === "not_available" || transcriptionStatus === "not available") {
      return <StatusCard status={transcriptionStatus} analysisType={type} />;
    }

    // If data exists, show it regardless of status
    if (!isDataEmpty(data, type)) {
      return renderData();
    }

    // If no data, determine what to show based on status and type
    let status = "unavailable";
    let analysisType = type;

    if (transcriptionStatus === "completed") {
      if (type === "summary") {
        return (
          <div className="d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
            <EmptyStateCard type="summary" />
          </div>
        );
      } else if (type === "key_insights") {
        return (
          <div className="d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
            <EmptyStateCard type="key_insights" />
          </div>
        );
      } else if (type === "action_items") {
        return (
          <div className="d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
            <EmptyStateCard type="action_items" />
          </div>
        );
      } else if (type === "transcription") {
        return (
          <div className="d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
            <EmptyStateCard type="transcription" />
          </div>
        );
      }
    } else if (transcriptionStatus === "analysis_failed") {
      if (type === "summary" || type === "key_insights" || type === "action_items") {
        status = "analysis_failed";
      } else if (type === "transcription") {
        // If transcription data exists but analysis failed, show empty state
        status = "unavailable";
      } else {
        status = "unavailable";
      }
    } else if (transcriptionStatus === "transcription_failed") {
      // When transcription failed - affects transcription data
      if (type === "transcription") {
        status = "transcription_failed";
      } else {
        // For analysis data, show unavailable since transcription failed
        status = "unavailable";
      }
    } else if (transcriptionStatus === "analysis_inprogress") {
      // When analysis is in progress
      if (type === "summary" || type === "key_insights" || type === "action_items") {
        status = "analysis_inprogress";
      } else if (type === "transcription") {
        // If transcription data exists but analysis is in progress, show empty state
        status = "unavailable";
      } else {
        status = "unavailable";
      }
    } else if (transcriptionStatus === "transcription_inprogress") {
      // When transcription is in progress
      status = "transcription_inprogress";
    } else if (transcriptionStatus === "queued") {
      // When in queue
      status = "queued";
    } else if (transcriptionStatus === "unavailable") {
      // When status is unavailable
      status = "unavailable";
    } else {
      // Default unavailable
      status = "unavailable";
    }

    return <StatusCard status={status} analysisType={analysisType} />;
  };

  const mainItems = [
    {
      key: '1',
      label: 'Key Insights / Takeaways',
      children: (
        <div className="p-3">
          {renderContent('key_insights', transcriptionData?.takeaways, () => (
             <div style={{ padding: '1rem', border: '1px solid #178fff', borderRadius: '10px', backgroundColor: '#fff' }}>
             <ul style={{ marginBottom: '0', fontSize: '16px', paddingLeft: '20px', listStyleType: 'disc' }}>

                {transcriptionData?.takeaways?.map((insight, index) => (
                  <li key={index} style={{
                    marginBottom: '0.5rem',
                    position: 'relative',
                    paddingRight: '80px'
                  }}>
                    <div style={{ paddingRight: '16px'}}>
                      {insight?.text}
                    </div>
                    <span style={{
                      position: 'absolute',
                      top: '0',
                      right: '0',
                      color: '#666',
                      whiteSpace: 'nowrap'
                    }}>
                      {formatTime(insight?.start_time)}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      )
    },
    {
      key: '2',
      label: 'Summary',
      children: (
        <div className="p-3">
          {renderContent('summary', transcriptionData?.summary, () => (
            <div style={{ padding: '1rem', border: '1px solid #178fff', borderRadius: '10px' , backgroundColor: '#fff' }}>
              <p style={{ fontSize: '16px' }}>
                {transcriptionData?.summary}
              </p>
            </div>
          ))}
        </div>
      )
    },
    {
      key: '3',
      label: 'Action Items',
      children: ( 
        <div className="p-3">
          {renderContent('action_items', transcriptionData?.action_items, () => (
         <div style={{ padding: '1rem', border: '1px solid #178fff', borderRadius: '10px' , backgroundColor: '#fff' }}>
         <ul className="mb-0 pt-2" style={{ fontSize: '16px', paddingLeft: '20px', listStyleType: 'disc' }}>

                {transcriptionData?.action_items?.map((action, index) => (
                  <li key={index} style={{
                    marginBottom: '0.5rem',
                    position: 'relative',
                    paddingRight: '80px'
                  }}>
                    <div style={{ paddingRight: '16px'}}>
                      {action?.text}
                    </div>
                    <span style={{
                      position: 'absolute',
                      top: '0',
                      right: '0',
                      color: '#666',
                      whiteSpace: 'nowrap'
                    }}>
                      {formatTime(action?.start_time)}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      )
    },
    {
      key: '4',
      label: 'Transcript',
      children: (
        <div className="p-3 h-100">
          {renderContent('transcription', transcriptionData?.transcription?.transcription, () => (
            <div className="transcript-container" style={{
              padding: '1rem',
              border: '1px solid #178fff',
              borderRadius: '10px',
              height: '100%',
              overflowY: 'auto',
              backgroundColor: '#fff'
            }}>

              {transcriptionData?.transcription?.transcription?.map((item, index) => {
                const isActive = currentTime >= item?.start_time &&
                  currentTime < (transcriptionData?.transcription?.transcription[index + 1]?.start_time || Infinity);

                return (
                  <TranscriptCard
                    key={index}
                    timestamp={formatTime(item?.start_time)}
                    content={item?.transcript}
                    isActive={isActive}
                    onSeek={() => handleSeek(item?.start_time)}
                  />
                );
              })}
            </div>
          ))}
        </div>
      )
    },
    {
      key: '5',
      label: `Participants (${participantsData?.rows?.length || 0})`,
      children: (
        <div className="p-3">
          {renderContent('participants', participantsData?.rows, () => (
            participantsData?.rows?.map((participant, index) => (
              <ParticipantCard
                key={index}
                name={participant?.screen_name}
                role={participant?.role}
                joinTime={moment(participant?.joined_at).format("HH:mm")}
                leaveTime={moment(participant?.leave_at).format("HH:mm")}
                index={index}
              />
            ))
          ))}
        </div>
      )
    }
  ];

  const sidebarItems = [
    {
      key: '1',
      label: 'Transcript',
      children: (
        <div className="p-3 h-100">
          {renderContent('transcription', transcriptionData?.transcription?.transcription, () => (
            <div className="transcript-container" style={{
              padding: '1rem',
              border: '1px solid #178fff',
              borderRadius: '10px',
              height: '100%',
              overflowY: 'auto',
               backgroundColor: '#fff'
            }}>

              {transcriptionData?.transcription?.transcription?.map((item, index) => {
                const isActive = currentTime >= item?.start_time &&
                  currentTime < (transcriptionData?.transcription?.transcription[index + 1]?.start_time || Infinity);

                return (
                  <TranscriptCard
                    key={index}
                    timestamp={formatTime(item?.start_time)}
                    content={item?.transcript}
                    isActive={isActive}
                    onSeek={() => handleSeek(item?.start_time)}
                  />
                );
              })}
            </div>
          ))}
        </div>
      ),
    },
    ...(transcriptionData?.is_share_user === false ? [{
      key: '2',
      label: `Participants (${participantsData?.rows?.length || 0})`,
      children: (
        <div className="p-3">
          {renderContent('participants', participantsData?.rows, () => (
            participantsData?.rows?.map((participant, index) => (
              <ParticipantCard
                key={index}
                name={participant?.screen_name}
                role={participant?.role}
                joinTime={moment(participant?.joined_at).format("HH:mm")}
                leaveTime={moment(participant?.leave_at).format("HH:mm")}
                index={index}
              />
            ))
          ))}
        </div>
      ),
    }] : []),
  ];

  const getFilteredItems = () => {
    if (!isSmallScreen) {
      return mainItems.filter(item => !['4', '5'].includes(item.key));
    }
    // For mobile view, filter out Participants tab if is_share_user is true
    return mainItems.filter(item => {
      if (item.key === '5' && transcriptionData?.is_share_user === true) {
        return false;
      }
      return true;
    });
  };

  // Show test component if showTest is true
  if (showTest) {
    return (
      <div>
        <button
          onClick={() => setShowTest(false)}
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 9999,
            padding: '10px 20px',
            backgroundColor: '#3B60E4',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Back to Transcript
        </button>
        <EmptyStateTest />
      </div>
    );
  }

  return (
    <div className="transcription-layout vh-100 d-flex flex-column">
      {/* Test Buttons - Remove these after testing */}
      <div style={{ position: 'fixed', top: '20px', right: '20px', zIndex: 9999, display: 'flex', gap: '10px' }}>
        <button
          onClick={() => setShowTest(true)}
          style={{
            padding: '10px 15px',
            backgroundColor: '#FF8E00',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          🧪 Test Cards
        </button>
        <button
          onClick={() => setForceEmptyState(!forceEmptyState)}
          style={{
            padding: '10px 15px',
            backgroundColor: forceEmptyState ? '#DC0000' : '#3B60E4',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          {forceEmptyState ? '❌ Stop Test' : '🔄 Force Empty'}
        </button>
      </div>

      <div className="flex-grow-1 overflow-hidden">
        <div className="container-fluid h-100 p-0">
          <div className="row h-100 m-0">
            <PanelGroup direction="horizontal" className="p-0">
              <Panel defaultSize={70} minSize={30}>
                <div className="left-section d-flex flex-column overflow-hidden h-100 p-0">
                  <VideoPlayer
                    ref={videoPlayerRef}
                    videoUrl={transcriptionData?.video_url}
                    onProgress={handleProgress}
                  />
                  <div className="bottom-container px-2 flex-grow-1 bg-white border-top d-flex flex-column overflow-auto" style={{ height: '100%' }}>
                    <TabHeader
                      items={getFilteredItems()}
                      activeKey={activeTab}
                      onChange={setActiveTab}
                    />
                    <div className="tabs-content" style={{ flex: 1 }}>
                      <div className="p-2">
                        {mainItems.map(item => (
                          <div key={item.key} style={{ display: item.key === activeTab ? 'block' : 'none' }}>
                            {item.children}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>

              {!isSmallScreen && (
                <>
                  <PanelResizeHandle className="resize-handle">
                    <div className="resize-handle-content">
                      <div className="resize-handle-dots">
                        <div className="dot" />
                        <div className="dot" />
                        <div className="dot" />
                      </div>
                    </div>
                  </PanelResizeHandle>

                  <Panel defaultSize={50} minSize={20}>
                    <Sidebar
                      items={sidebarItems}
                      activeTab={sidebarTab}
                      onTabChange={setSidebarTab}
                      onDownload={handleDownload}
                      eventName={transcriptionData?.event_name}
                    />
                  </Panel>
                </>
              )}
            </PanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TranscriptionPage;