import React from 'react';
import { EmptyStateCard } from './StatusCard';
import './StatusCard.scss';

// Test component to preview all empty state cards
function EmptyStateTest() {
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f5f5f5', 
      minHeight: '100vh' 
    }}>
      <h2 style={{ textAlign: 'center', marginBottom: '30px' }}>
        Empty State Cards Test
      </h2>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', 
        gap: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        
        <div style={{ textAlign: 'center' }}>
          <h3>Key Insights Empty State</h3>
          <EmptyStateCard type="key_insights" />
        </div>

        <div style={{ textAlign: 'center' }}>
          <h3>Summary Empty State</h3>
          <EmptyStateCard type="summary" />
        </div>

        <div style={{ textAlign: 'center' }}>
          <h3>Action Items Empty State</h3>
          <EmptyStateCard type="action_items" />
        </div>

        <div style={{ textAlign: 'center' }}>
          <h3>Transcription Empty State</h3>
          <EmptyStateCard type="transcription" />
        </div>

      </div>

      <div style={{ 
        marginTop: '40px', 
        padding: '20px', 
        backgroundColor: 'white', 
        borderRadius: '10px',
        textAlign: 'center'
      }}>
        <h3>Instructions:</h3>
        <p>These cards will appear in the transcript page when:</p>
        <ul style={{ textAlign: 'left', maxWidth: '600px', margin: '0 auto' }}>
          <li>The transcription status is "completed"</li>
          <li>But there's no actual data for that specific section</li>
          <li>They replace the old plain text empty state messages</li>
        </ul>
      </div>
    </div>
  );
}

export default EmptyStateTest;
