import React from 'react';
import TabHeader from './TabHeader';
// import DownloadButton from './DownloadButton';


function Sidebar({ items, activeTab, onTabChange, /* onDownload, */ eventName }) {
  return (
    <div className="sidebar-container bg-white h-100 p-0 d-flex flex-column">
      <div className="sidebar-header p-3 border-bottom d-flex justify-content-between align-items-center">
        <div>
          <h5 className="mb-0" style={{ color: '#3B60E4' }}>Recorded Meetings <span style={{ fontSize: '14px' }}>|</span> {eventName || 'Detailed Analysis'}</h5>
          <p className="text-muted mb-0" style={{ color: '#3B60E4', fontSize: '12px' }}>Timeline-based analysis points</p>
        </div>
        <div className="d-flex align-items-center">
          {/* <AskAIButton /> */}
          {/* <DownloadButton onClick={onDownload} /> */}
        </div>
      </div>
      
      <TabHeader 
        items={items}
        activeKey={activeTab}
        onChange={onTabChange}
      />
      
      <div className="sidebar-content flex-grow-1 overflow-auto">
        <div className="p-2">
          {items.map(item => (
            <div key={item.key} style={{ display: item.key === activeTab ? 'block' : 'none' }}>
              {item.children}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Sidebar; 