$font: "Inter", sans-serif;

.recordings {
  &-container {
    width: 100%;
    background: white;
    // padding: 20px;
    border-radius: 8px;
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 20px;
    border-bottom: 1px solid #bebebe;
    padding: 10px 20px;
    // position: fixed;
    width: 100%;
    background: white;
    top: 0;
    z-index: 100;
    height: 3.5rem;
    &-separator {
      width: 2px;
      height: 20px;
      background-color: #3b60e4;
      // .recordings-header-refresh-icon-loading{
      //   animation: spin 1s linear infinite;
      // }
    }
    &-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      svg {
        font-size: 20px;
        cursor: pointer;
      }
    }
    &-info {
      display: flex;
      // margin-top: 3.7rem;
      height: 3rem;
      align-items: center;
      padding-left: 40px;
      gap: 0.5rem;
      &-icon {
        font-size: 18px;
      }
      p {
        margin: 0;
        font-size: 13px;
        font-weight: 400;
        color: #4b4b4b;
      }
    }
    &-title {
      display: flex;
      align-items: center;
      color: #3b60e4;
      gap: 0.5rem;
      h1 {
        margin: 0;
        font-size: medium;
      }
    }
    .search-recordings {
      width: 300px;
      input {
        border: none;
        background-color: #f6f6f6;
        border-radius: 15px;
        padding: 8px;
      }
      .ant-input-group-addon {
        background-color: #f6f6f6;
        border-radius: 0 15px 15px 0;
        button {
          background-color: #3b60e4;
          border-radius: 15px !important;
          color: white;
          font-size: 18px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 18px;
          height: 37px;
          span {
            color: white;
          }
        }
      }
    }
  }
  &-row {
    background-color: #fff;
    border-radius: 10px;
    // box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    td:first-child {
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
    }
    .null {
      span {
        background-color: #e0e5ff !important;
        color: #3987e6 !important;
        padding: 3px 10px;
        border-radius: 15px;
      }
    }
    .not_available,
    .unavailable {
      span {
        background-color: #eae0ff !important;
        color: #7e47eb !important;
        padding: 3px 10px;
        border-radius: 15px;
      }
    }
    .failed,
    // .null,
    .analysis_failed {
      span {
        background-color: #ffe1e1 !important;
        color: #dc0000 !important;
        padding: 3px 10px;
        border-radius: 15px;
      }
    }
    .successed {
      span {
        background-color: #e1fcef !important;
        color: #14804a !important;
        padding: 3px 10px;
        border-radius: 15px;
      }
    }
    .inprocess,
    .analysis_inprogress {
      span {
        background-color: #fdfac7 !important;
        color: #ffc700 !important;
        padding: 3px 10px;
        border-radius: 15px;
      }
    }
    .queued {
      span {
        background-color: #ffe6c6 !important;
        color: #ff8e00 !important;
        padding: 3px 10px;
        border-radius: 15px;
      }
    }

    td:last-child {
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
    }
  }
  &-table {
    // margin-top: 5rem;
    width: 100%;
    padding: 0 20px;
    background-color: #f4f8f9;
    max-height: calc(100svh - 6.7rem);
    overflow: auto;
    table {
      width: 100%;
      border-spacing: 0 15px; /* Adds vertical spacing between rows */
      border-collapse: separate; /* Necessary for border-spacing to work */
      tbody {
      }
      th {
        font-weight: normal;
        font-size: 15px;
        padding: 15px;
        color: #414141;
        text-align: center;
        &:nth-child(2) {
          text-align: left;
          padding-left: 3rem;
        }
      }
      .completed {
        span {
          background-color: #e1fcef !important;
          color: #14804a !important;
          padding: 3px 10px;
          border-radius: 15px;
        }
      }
      td {
        padding: 15px;
        text-align: left;
        font-size: 13px;
        background-color: white;
        display: table-cell;
        vertical-align: middle;
        color: #4b4b4b;
        .recording-clip-container {
          display: flex;
          cursor: pointer;
          align-items: center;
          gap: 1rem;
        }
        &:nth-child(7) {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          color: #6a6969 !important;
          gap: 1rem;
          svg {
            cursor: pointer;
            width: 20px;
            height: 20px;
            &:nth-child(3) {
              width: 22px;
              height: 22px;
            }
          }
        }
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(6) {
          text-align: center;
          color: black;
        }
        &:nth-child(2) {
          display: flex;
          width: 450px;
          align-items: center;
          gap: 1rem;
          .record-icon {
            width: auto;
            height: 18px;
          }
          img {
            width: auto;
            height: 65px;
          }
          .recording-details {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            justify-content: center;
            h3 {
              font-size: 16px;
              margin: 0;
            }
            p {
              font-size: 12px;
              margin: 0;
            }
          }
        }
      }
    }
  }
}
.share-modal {
  width: 35rem !important;
  .ant-modal-body {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 1.5rem;
    div {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    &-title {
      color: #3b60e4;
      font-size: 18px;
      font-weight: bold;
    }
  }
  &-copy {
    color: #3b60e4;
    cursor: pointer;
  }
  .access {
    &-options {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    &-option {
      display: flex;
      color: #626262;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      div {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }
    }
    &-section {
      display: flex;
      flex-direction: column;
      span {
        border-bottom: 1px solid #bebebe;
        font-size: 14px;
        color: #626262;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
      }
    }
  }
  .invite-users {
    display: flex;
    gap: 1rem;
    align-items: center;
    .user {
      &-invited {
        font-weight: 600;
        font-family: $font;
        font-size: 14px;
      }
      &-avatar {
        background-color: red;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        position: relative;
        &-verified {
          background-color: #3b60e4;
          color: white;
          position: absolute;
          bottom: 0;
          right: 0;
          border-radius: 50%;
          font-size: 12px;
          padding: 1px;
        }
        &-remove {
          position: absolute;
          top: -9px;
          right: -9px;
          background-color: #888888;
          border-radius: 50%;
          color: white;
        }
      }
      &-avatars {
        display: flex;
        gap: 1rem;
        align-items: center;
      }
    }
  }
  .input-section {
    display: flex;
    gap: 2rem;
    .invite-btn {
      border: #4253f0 1px solid;
      background-color: #e1e8ff;
      color: #3b60e4;
      padding: 0.5rem 1.5rem;
      border-radius: 18px;
    }
    .ant-select-focused {
      .ant-select-selector {
        border: none;
        box-shadow: none;
        border-right-width: 0;
      }
    }
    .invite {
      &-email {
        .ant-select-selector {
          padding: 0 10px !important;
          border-radius: 18px 0 0 18px !important;
          display: flex;
          position: relative;
          align-items: center;
          height: 50px;
          background-color: #f6f6f6 !important;
          .ant-select-selection-overflow {
            padding: 0 !important;
            height: auto;
            gap: 10px;
            .ant-select-selection-overflow-item {
              height: auto;
              .ant-select-selection-item {
                height: 30px;
                margin: 0;
                display: flex;
                align-items: center;
                background-color: #e2e2e2;
                border-radius: 10px;
                gap: 10px;
                .ant-select-selection-item-content {
                  height: auto;
                }
              }
            }
          }
        }
      }
    }
  }
}
.no-subscription {
  padding: 2rem;
  background: linear-gradient(
    90deg,
    #3b60e44d,
    #b4d9ffcc,
    #b4d9ffcc,
    #3b60e44d
  );
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 2rem;
  &-icon {
    width: 70px;
    height: 70px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    svg {
      background-color: #5458f7;
      color: white;
      border-radius: 50%;
      width: 35px;
      height: 35px;
    }
  }
  &-content {
    width: 60%;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
    p {
      text-align: center;
    }
    a {
      color: #3b60e4;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Custom checkbox styling (Bootstrap-like approach)
.custom-checkbox-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  user-select: none;

  .custom-checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    &:checked ~ .custom-checkbox-checkmark {
      background-color: #3b60e4;
      border-color: #3b60e4;

      &::after {
        display: block;
      }
    }

    &:indeterminate ~ .custom-checkbox-checkmark {
      background-color: #3b60e4;
      border-color: #3b60e4;

      &::after {
        display: block;
        content: "";
        width: 8px;
        height: 2px;
        background-color: #fff;
        border: none;
        transform: none;
        left: 50%;
        top: 50%;
        margin-left: -4px;
        margin-top: -1px;
      }
    }
  }

  .custom-checkbox-checkmark {
    position: relative;
    height: 16px;
    width: 16px;
    background-color: transparent;
    border: 2px solid #3b60e4;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &::after {
      content: "";
      position: absolute;
      display: none;
      left: 50%;
      top: 50%;
      width: 6px;
      height: 10px;
      border: solid #fff;
      border-width: 0 2px 2px 0;
      transform: translate(-50%, -60%) rotate(45deg);
    }
  }

  &:hover .custom-checkbox-checkmark {
    border-color: #2a4bc7;
    box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.1);
  }
}
