import React from "react";
import { IoRefreshOutline } from "react-icons/io5";
import { Input, Tooltip } from "antd";
import { RiDeleteBin6Line } from "react-icons/ri";
import { FiDownload } from "react-icons/fi";
import { ANALYSIS_STATUS } from "../../../../../utils/constants";

const { Search } = Input;

/**
 * @typedef {Object} TranscriptionHeaderProps
 * @property {Array} subRecordings - List of all recordings
 * @property {Array} filteredRecordings - List of filtered recordings
 * @property {Function} getRecordings - Function to fetch recordings
 * @property {boolean} isLoading - Whether recordings are being loaded
 * @property {Function} onSearch - Function to handle search
 * @property {boolean} showDelete - Whether to show delete button
 * @property {Function} handleDelete - Function to handle deletion
 * @property {boolean} showDownload - Whether to show download button
 * @property {Function} handleDownload - Function to handle download
 * @property {string} eventName - Name of the event
 */

export default function TranscriptionHeader({
  filteredRecordings,
  getRecordings,
  isLoading,
  onSearch,
  showDelete,
  handleDelete,
  showDownload,
  handleDownload,
  eventName,
}) {
  const hasQueuedRecordings = filteredRecordings?.some(
    (item) =>
      item?.transacrption_status === ANALYSIS_STATUS.QUEUED ||
      item?.transacrption_status === ANALYSIS_STATUS.TRANSCRIPTION_INPROGRESS ||
      item?.transacrption_status === ANALYSIS_STATUS.ANALYSIS_INPROGRESS
  );

  return (
    <header className="recordings-header">
      <div className="recordings-header-title">
        <h1>Recorded Meetings{eventName ? ` | ${eventName}` : ''}</h1>
        <div className="recordings-header-actions">
          {hasQueuedRecordings && (
            <>
              <span className="recordings-header-separator" />
              <Tooltip title="Refresh">
                <IoRefreshOutline
                  onClick={getRecordings}
                  className={isLoading ? "recordings-header-refresh-icon-loading" : ""}
                  style={{
                    transition: "transform 0.5s linear",
                    transform: isLoading ? "rotate(360deg)" : "rotate(0deg)",
                  }}
                />
              </Tooltip>
            </>
          )}
        </div>
        {showDelete && (
          <>
            |{" "}
            <RiDeleteBin6Line
              className="action-icons action-icons-delete"
              onClick={handleDelete}
              style={{ cursor: "pointer" }}
            />
          </>
        )}
        {showDownload && (
          <>
            | <FiDownload
                onClick={handleDownload}
                style={{ cursor: "pointer" }}
                className="action-icons action-icons-download"
              />
          </>
        )}
      </div>
      <Search
        placeholder="Search recordings"
        onSearch={onSearch}
        className="search-recordings"
      />
    </header>
  );
}
