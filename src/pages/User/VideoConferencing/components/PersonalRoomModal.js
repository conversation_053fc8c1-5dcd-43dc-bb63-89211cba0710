import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { VideoConferenceService, CommonServices } from "../../../../services";
import {
  baseUrlGenerator,
  encoder,
  modalNotification,
} from "../../../../utils";
import { ReactComponent as CopyLink } from "../Assets/CopyLink.svg";
import { ReactComponent as CopyLinkIcon } from "../Assets/CopyLinkIcon.svg";
import userRoutesMap from "../../../../routeControl/userRoutes";
import "./PersonalRoomModal.scss";

export default function PersonalRoomModal({
  startMeetingModal,
  setStartMeetingModal,
}) {
  const [eventName, setEventName] = useState("");
  const [eventMode, setEventMode] = useState("follow");
  const [editPersonalMeeting, setEditPersonalMeeting] = useState(false);
  const [personalMeetingRoomId, setPersonalMeetingRoomId] = useState("");
  const [personalMeetingRoomUid, setPersonalMeetingRoomUid] = useState("");
  const [originalEventName, setOriginalEventName] = useState("");
  const [originalEventMode, setOriginalEventMode] = useState("");
  const [sipData, setSipData] = useState({});
  const [roomNameError, setRoomNameError] = useState("");

  const getMeetingSipData = async (roomUid) => {
    if (!roomUid) return;

    try {
      const res = await CommonServices.sipDetails(roomUid);
      const { success, message, data } = res;
      if (success === 1) {
        setSipData(data);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      console.error("Error fetching SIP data:", error);
    }
  };

  const getPersonalMeetingRoomDetails = async () => {
    const res = await VideoConferenceService.getPersonalMeetingRoomIdService();
    const { success, message, data } = res;
    if (success === 1) {
      setPersonalMeetingRoomId(data?.id);
      setPersonalMeetingRoomUid(data?.room_uid);
      setEventName(data?.event_name);
      setEventMode(data?.event_mode);

      // Fetch SIP data for the personal meeting room
      await getMeetingSipData(data?.room_uid);
    } else {
      modalNotification({
        type: "error",
        message,
      });
    }
  };

  const updatePersonalMeetingRoomDetails = async () => {
    // Validate room name is not empty
    if (!eventName || eventName.trim() === "") {
      setRoomNameError("Enter the room name");
      return;
    }

    // Clear any existing errors
    setRoomNameError("");

    const res = await VideoConferenceService.updatePlanMeetingsService(
      personalMeetingRoomId,
      {
        event_name: eventName.trim(),
        event_mode: eventMode,
      }
    );
    const { success, message } = res;
    if (success === 1) {
      modalNotification({
        type: "success",
        message,
      });
      getPersonalMeetingRoomDetails();
      setEditPersonalMeeting(false);
      setRoomNameError("");
    } else {
      modalNotification({
        type: "error",
        message,
      });
    }
  };

  const handleRoomNameChange = (e) => {
    setEventName(e.target.value);
    // Clear error when user starts typing
    if (roomNameError) {
      setRoomNameError("");
    }
  };

  const copyDetailedInvitation = () => {
    const meetingUrl = `${baseUrlGenerator(
      userRoutesMap.DAAKIA_VC_MEET.path
    )}/${encoder(personalMeetingRoomUid)}`;

    const currentDate = moment().format("DD MMM YYYY");
    const currentTime = moment().format("HH:mm");

    const copyInvitation = 
`Event Name: ${eventName}
Date: ${currentDate}
Time: ${currentTime} IST
Type: Personal Room (Available anytime)

${meetingUrl}

Join by phone
${(sipData?.sip_numbers || [])
  .map(
    (number) =>
      `${number?.formatted_phone?.split(" (")[0]} (${
        number?.formatted_phone?.split(" (")[1] || ""
      }`
  )
  .join("\n")}

Join by video system
Dial ${sipData?.sip_dailing_ip || "N/A"}

PIN: ${sipData?.sip_pin || "N/A"} (Enter this PIN after dialling in)`;

    window.navigator.clipboard.writeText(copyInvitation);
    modalNotification({
      type: "success",
      message: "Meeting invitation copied to clipboard",
    });
  };

  useEffect(() => {
    if (startMeetingModal) {
      getPersonalMeetingRoomDetails();
    }
  }, [startMeetingModal]);

  return (
    <Modal
      open={startMeetingModal}
      onCancel={() => {
        setStartMeetingModal(false);
        // If the modal is closed while editing, revert the eventName to the original value
        if (editPersonalMeeting) {
          setEventName(originalEventName);
          setEventMode(originalEventMode);
          setEditPersonalMeeting(false);
          setRoomNameError("");
        }
      }}
      width="80%"
      footer={null}
      className="personal-meeting-modal"
    >
      <div className="personal-meeting-modal-content">
        <h2 className="personal-meeting-modal-content-title">
          Your Personal Room Link
        </h2>
        <p className="personal-meeting-modal-content-subtitle">
          Start or share this quick access room anytime.
        </p>

        <div className="personal-meeting-modal-content-input">
          <div className="personal-meeting-modal-content-input-group">
            <fieldset className={`custom-fieldset ${roomNameError ? "error" : ""}`}>
              <legend className="custom-legend">
                Room Name
                {editPersonalMeeting && <span className="required">*</span>}
              </legend>
              <Tooltip
                title={
                  editPersonalMeeting
                    ? "Edit your Personal Room Name."
                    : "Click edit to change room name."
                }
                placement="top"
              >
                <input
                  type="text"
                  className={`custom-input ${
                    editPersonalMeeting ? "editing" : ""
                  } ${roomNameError ? "error" : ""}`}
                  value={eventName}
                  onChange={handleRoomNameChange}
                  required
                  placeholder={eventName}
                  readOnly={!editPersonalMeeting}
                />
              </Tooltip>
            </fieldset>
            {roomNameError && editPersonalMeeting && (
              <div className="error-message">{roomNameError}</div>
            )}
          </div>
          <div className="personal-meeting-modal-content-input-group">
            <fieldset className="custom-fieldset">
              <legend className="custom-legend">
                Invite Link
                {editPersonalMeeting && <span className="required">*</span>}
              </legend>
              <div className="invite-link-content">
                <CopyLinkIcon style={{ marginRight: "8px" }} />
                {(() => {
                  const fullUrl = `${baseUrlGenerator(
                    userRoutesMap.DAAKIA_VC_MEET.path
                  )}/${encoder(personalMeetingRoomUid)}`;
                  // Show the first (fullUrl.length - 15) chars, then "..." at the end if long
                  const displayUrl =
                    fullUrl.length > 18
                      ? `${fullUrl.slice(0, fullUrl.length - 13)}...`
                      : fullUrl;
                  return (
                    <a
                      href={fullUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="personal-meeting-modal-link"
                    >
                      {displayUrl}
                    </a>
                  );
                })()}
                <span
                  className="personal-meeting-modal-copyicon"
                  onClick={() => {
                    const url = `${baseUrlGenerator(
                      userRoutesMap.DAAKIA_VC_MEET.path
                    )}/${encoder(personalMeetingRoomUid)}`;
                    window.navigator.clipboard.writeText(url);
                    modalNotification({
                      type: "success",
                      message: "Link copied to clipboard",
                    });
                  }}
                  title="Copy link"
                >
                  <CopyLink />
                </span>
              </div>
            </fieldset>
          </div>
          <div className="personal-meeting-modal-content-input-group copy-invitation-group">
            <button
              className="action-btn-personal-room copy-invitation"
              onClick={copyDetailedInvitation}
              title="Copy meeting invitation"
            >
              Copy Invitation
            </button>
          </div>
        </div>
        <div className="personal-meeting-modal-content-section">
          <label className="section-label">Meeting Mode</label>
          <div className="radio-group">
            <Tooltip
              title={
                editPersonalMeeting ? (
                  <div style={{ textAlign: "center" }}>
                    Participants will automatically enter the meeting once
                    started by host.
                  </div>
                ) : (
                  "Click edit to change meeting mode."
                )
              }
              placement="top"
            >
              <label className="radio-label">
                <input
                  type="radio"
                  name="security"
                  value="follow"
                  checked={eventMode === "follow"}
                  onChange={() => editPersonalMeeting && setEventMode("follow")}
                  disabled={!editPersonalMeeting}
                />
                Follow Mode
              </label>
            </Tooltip>
            <Tooltip
              title={
                editPersonalMeeting ? (
                  <div style={{ textAlign: "center" }}>
                    Participants can start the meeting without having to wait
                    for the host.
                  </div>
                ) : (
                  "Click edit to change meeting mode."
                )
              }
              placement="top"
            >
              <label className="radio-label">
                <input
                  type="radio"
                  name="security"
                  value="participant"
                  checked={eventMode === "participant"}
                  onChange={() =>
                    editPersonalMeeting && setEventMode("participant")
                  }
                  disabled={!editPersonalMeeting}
                />
                Participant Mode
              </label>
            </Tooltip>
            <Tooltip
              title={
                editPersonalMeeting ? (
                  <div style={{ textAlign: "center" }}>
                    Participants will be waiting in the lobby until host admits
                    them.
                  </div>
                ) : (
                  "Click edit to change meeting mode."
                )
              }
              placement="top"
            >
              <label className="radio-label">
                <input
                  type="radio"
                  name="security"
                  value="lobby"
                  checked={eventMode === "lobby"}
                  defaultChecked={eventMode === "lobby"}
                  onChange={() => editPersonalMeeting && setEventMode("lobby")}
                  disabled={!editPersonalMeeting}
                />
                Lobby Mode
              </label>
            </Tooltip>
          </div>
        </div>

        {/* <div className="personal-meeting-modal-content-section">
            <label className="section-label">Audio</label>
            <div className="radio-group">
              <label className="radio-label">
                <input type="radio" name="audio" defaultChecked style={{ accentColor: "#3B60E4" }} />
                Computer Audio
              </label>
            </div>
          </div>
          <div className="personal-meeting-modal-content-section video-section">
            <div className="video-section-column">
              <div
                className="video-section-row"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "32px", // Ensures same gap between Video and Host as Host and radio
                }}
              >
                <label className="section-label" style={{ margin: 0 }}>
                  Video
                </label>
                <span className="video-role" style={{ margin: 0 }}>
                  Host
                </span>
                <label className="radio-label">
                  <input
                    type="radio"
                    name="host-video"
                    defaultChecked
                    style={{ accentColor: "#3B60E4" }}
                  />
                  <span className="video-toggle">On</span>
                </label>
              </div>
              <div
                className="video-section-row participant-row"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "32px", // Ensures same gap between Participant and radio
                  marginTop: "24px", // Optional: vertical spacing between rows
                }}
              >
                <span className="video-role" style={{ margin: 0 }}>
                  Participant
                </span>
                <label className="radio-label">
                  <input
                    type="radio"
                    name="participant-video"
                    style={{ accentColor: "#3B60E4" }}
                  />
                  <span className="video-toggle">Off</span>
                </label>
              </div>
            </div>
          </div> */}

        <div className="personal-meeting-modal-content-actions">
          <button
            className="action-btn-personal-room start"
            disabled={editPersonalMeeting}
            onClick={() => {
              const url = `${baseUrlGenerator(
                userRoutesMap.DAAKIA_VC_MEET.path
              )}/${encoder(personalMeetingRoomUid)}`;
              window.open(url, "_blank", "noopener,noreferrer");
            }}
          >
            Start Meeting
          </button>
          {editPersonalMeeting ? (
            <>
              <button
                className="action-btn-personal-room save"
                onClick={updatePersonalMeetingRoomDetails}
              >
                Save
              </button>
              <button
                className="action-btn-personal-room discard"
                onClick={() => {
                  setEventName(originalEventName);
                  setEventMode(originalEventMode);
                  setEditPersonalMeeting(false);
                  setRoomNameError("");
                }}
              >
                Discard Changes
              </button>
            </>
          ) : (
            <button
              className="action-btn-personal-room edit"
              onClick={() => {
                setOriginalEventName(eventName);
                setOriginalEventMode(eventMode);
                setEditPersonalMeeting(true);
                setRoomNameError("");
              }}
            >
              Edit
            </button>
          )}
        </div>
      </div>
      {/* Redesigned Personal Meeting URL Modal */}
      {/* <div className="personal-meeting-modal-content">
          <h1 className="personal-meeting-modal-title">{t("text.videoConferencing.personalMeetingUrl")}</h1>
          <div className="personal-meeting-modal-link-box">
            <a
              href={`${baseUrlGenerator(userRoutesMap.DAAKIA_VC_MEET.path)}/${encoder(personalMeetingRoomId)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="personal-meeting-modal-link"
            >
              {`${baseUrlGenerator(userRoutesMap.DAAKIA_VC_MEET.path)}/${encoder(personalMeetingRoomId)}`}
            </a>
            <span
              className="personal-meeting-modal-copy"
              onClick={() => {
                const url = `${baseUrlGenerator(userRoutesMap.DAAKIA_VC_MEET.path)}/${encoder(personalMeetingRoomId)}`;
                window.navigator.clipboard.writeText(url);
                modalNotification({
                  type: "success",
                  message: "Link copied to clipboard",
                });
              }}
              title="Copy link"
            >
              <IoMdCopy size={22} />
            </span>
          </div>
        </div> */}
    </Modal>
  );
}
