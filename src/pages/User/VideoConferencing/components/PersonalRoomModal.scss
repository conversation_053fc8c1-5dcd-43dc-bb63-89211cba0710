$font: "Inter", sans-serif;

.personal-meeting-modal{
    .ant-modal-content{
        border-radius: 16px;
    }
    .ant-modal-close-x{
      margin-top:15px !important;
      margin-right:15px !important;
    }
}

.personal-meeting-modal-copyicon{
  margin-left: 8px;
  cursor: pointer;
}

.personal-meeting-modal-content{
    padding: 15px;
    padding-top: 0;
    &-title{
    //   margin-top: 20px;
      font-family: $font;
      padding: 12px 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      color: #4F4F4F;
      @media screen and (max-width: 600px ){
        display: flex;
        justify-content: center;
      }
    }
    &-subtitle{
      font-family: $font;
      font-size: 16px;
      font-weight: 600;
      color: #6C757D;
      padding: 12px 0 12px 0;
      margin-bottom: 20px;
      @media screen and (max-width: 600px ){
        display: flex;
        justify-content: center;
      }
    }
    &-input{
      font-family: $font;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      &-group{
        flex: 1;
        display: flex;
        flex-direction: column;
   
        .label{
          font-weight: 400;
          font-size: 14px;
          .span{
            color: #E00000;
          }
        }
      }
    }
   
  }
  .personal-meeting-modal-content-input {
    font-family: $font;
    display: flex;
    gap: 15px;
    padding: 15px 0 15px 0;
    width: 100%;
    box-sizing: border-box;
    &-group {
      display: flex;
      align-items: flex-end;
      
      &:nth-child(1) {
        flex: 1.2;
        min-width: 200px;
      }
      
      &:nth-child(2) {
        flex: 1.5;
        min-width: 250px;
      }
      
      &.copy-invitation-group {
        flex: 0 0 auto;
        min-width: 160px;
        max-width: 180px;
        
        .action-btn-personal-room.copy-invitation {
          font-family: $font;
          background: #fff;
          color: #3B60E4;
          border: 1.5px solid #3B60E4;
          font-weight: 450;
          font-size: 16px;
          padding: 13.5px;
          border-radius: 8px;
          width: 100%;
          height: 57px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background 0.2s, color 0.2s;
          white-space: nowrap;
          
          &:hover {
            background: #3B60E4;
            color: #fff;
          }
        }
      }
    }
  }
   
  .personal-meeting-modal-content-section {
    display: flex;
    align-items: center;
    gap: 35px;
    margin-bottom: 20px;
    padding: 30px 0 0 0;
    .section-label {
      font-family: $font;
      font-weight: 600;
      font-size: 16px;
      color: #4F4F4F;
      min-width: 90px;
      margin: 0;
      display: block;
    }
    .radio-group {
      display: flex;
      gap: 30px;
      margin: 0;
      .radio-label {
        font-family: $font;
        font-weight: 400;
        font-size: 15px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 10px;
        input{
          &:checked{
            &:disabled {
              accent-color: #000; // Updated radio button icon color for disabled state
              background-color: #000; // Optional: lighter background for disabled
              border-color: #000; // Optional: border color for disabled
            }
            accent-color: #5770ff;
          }
        }
      }
    }
    // .video-options {
    //   display: flex;
    //   gap: 32px;
    //   .video-option {
    //     display: flex;
    //     align-items: center;
    //     gap: 8px;
    //     span {
    //       font-weight: 500;
    //       margin-right: 6px;
    //     }
    //   }
    // }
  }
   
  .personal-meeting-modal-content-actions {
    display: flex;
    gap: 16px;
    margin-top: 32px;
    padding: 30px 0 10px 0;
    .action-btn-personal-room {
      min-width: 120px;
      padding: 4px 14px;
      border-radius: 8px;
      // border: none;
      font-size: 18px;
      // font-weight: 500;

      &.edit, &.cancel, &.start, &.save, &.discard , &.copy {
        font-family: $font;
        background: #fff;
        color: #3B60E4;
        border: 1.5px solid #3B60E4;
        font-weight: 450;
        transition: background 0.2s, color 0.2s;
        &:hover {
          background: #3B60E4;
          color: #fff;
        }
      }
    }
  }
  
  .custom-fieldset {
    border: 1.5px solid #bdbdbd;
    border-radius: 8px;
    margin: 0;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 12px;
    background: #fff;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
    legend {
      width: auto;
      padding: 0 4px;
      margin-left: 12px;
    }
  }
  
  .custom-legend {
    font-size: 15px;
    color: #666;
    font-family: inherit;
    padding: 0 4px;
    margin-left: 12px;
    height: 20px;
    line-height: 20px;
    display: flex;
    align-items: center;
    background: #fff;
    /* Embed the legend inside the border */
    position: absolute;
    top: -12px;
    left: 16px;
    z-index: 1;
    pointer-events: none;
  }
  
  .custom-input {
    font-family: $font;
    flex: 1;
    padding: 13px;
    border: none;
    outline: none;
    width: 100%;
    font-size: 15px;
    background: transparent;
    margin-left: 0;
    font-family: inherit;
    color: #848484;
    min-height: 54px;
    min-width: 0;
    box-sizing: border-box;
    word-break: break-word;
    white-space: pre-line;
    cursor: not-allowed;
    &.editing{
      color: black;
      cursor: text;
    }
  }
  
  .required {
    color: #e53935;
    margin-left: 2px;
    font-size: 15px;
    position: relative;
    top: -2px;
  }
  
  .invite-link-content {
    display: flex;
    align-items: center;
    background: transparent;
    border: none;
    width: 100%;
    min-width: 0;
    padding: 11px;
    min-height: 54px;
    box-sizing: border-box;
    gap: 8px;
  }
  
  .personal-meeting-modal-link {
    color: #3B60E4;
    font-weight: 400;
    font-size: 15px;
    word-break: break-all;
    text-decoration: underline;
    flex: 1 1 0%;
    min-width: 0;
    max-width: 100%;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
  .action-btn-personal-room.start:disabled {
    cursor: not-allowed;
    opacity: 0.6; 
    &:hover{
      background: #fff ;
      color: #3B60E4 ;
      border: 1.5px solid #3B60E4 ;
    }
  }
  
  // .personal-meeting-modal-content-section.video-section {
  //   align-items: flex-start;
  //   padding-top: 30px;
  //   .video-section-column {
  //     display: flex;
  //     flex-direction: column;
  //     flex: 1;
  //   }
  //   .video-section-row {
  //     display: flex;
  //     align-items: center;
  //     gap: 32px;
  //     &.participant-row {
  //       margin-left: 122px;
  //       margin-top: 32px;
  //     }
  //   }
  //   .section-label {
  //     min-width: 90px;
  //     margin-right: 32px;
  //     margin-bottom: 0;
  //   }
  //   .video-role {
  //     min-width: 90px;
  //     color: #4F4F4F;
  //     font-weight: 500;
  //     font-size: 16px;
  //   }
  //   .video-toggle {
  //     color: #4F4F4F;
  //     font-weight: 400;
  //     font-size: 15px;
  //   }
  //   .radio-label{
  //     display: flex;
  //     align-items: center;
  //     gap: 8px;
  //     margin: 0;
  //   }
  //   .video-role{
  //     display: flex;
  //     align-items: center;
  //     gap: 8px;
  //     margin: 0;
  //   }
  // }
  
@media screen and (max-width: 600px) {
  .personal-meeting-modal-content-title,
  .personal-meeting-modal-content-subtitle {
    display: flex;
    justify-content: center;
    text-align: center;
    width: 100%;
  }

  .personal-meeting-modal-content-input {
    flex-direction: column;
    gap: 16px;
    min-width: 0;
    max-width: 100%;
    width: 100%;
    align-items: stretch;
    &-group {
      min-width: 0;
      max-width: 100%;
      width: 100%;
      flex: none;
      
      &:nth-child(1),
      &:nth-child(2) {
        min-width: 0;
      }
      
      &.copy-invitation-group {
        max-width: 100%;
        min-width: 0;
        
        .action-btn-personal-room.copy-invitation {
          width: 100%;
          min-width: 0;
          max-width: 100%;
        }
      }
    }
  }

  .custom-fieldset,
  .invite-link-content {
    max-width: 100%;
    min-width: 0;
    width: 100%;
    height: 54px;
    box-sizing: border-box;
  }
  
  .custom-fieldset {
    min-height: 54px;
    max-height: 54px;
  }
  
  .invite-link-content {
    min-height: 54px;
    max-height: 54px;
    padding: 11px;
  }
  
  .custom-input {
    min-height: 54px;
    max-height: 54px;
    height: 54px;
    line-height: normal;
  }

  .personal-meeting-modal-content-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 20px 0 0 0;
    .radio-group {
      flex-direction: column;
      gap: 10px;
      width: 100%;
    }
  }

  .personal-meeting-modal-content-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
    .action-btn {
      width: 100%;
      min-width: 0;
    }
  }
}

// Error styles
.custom-fieldset {
  &.error {
    border-color: #e53935;
  }
}

.custom-input {
  &.error {
    color: #e53935;
  }
}

.error-message {
  font-family: $font;
  color: #e53935;
  font-size: 14px;
  margin-top: 4px;
  font-weight: 400;
  margin-left: 0;
  margin-right: 10px;
}
  