$color-primary: #3b60e4;
$color-secondary: #4a4a4a;
$color-tertiary: #434343;
$active-card-color: #e9eeff;
$card-subtitle-color: #979797;
$card-border-color: #cccccc;
$required-color: #e14045;
$body-border-color: #c5c5c5;
$document-upload-border-color: #9e9e9e;
$font: "Inter", sans-serif;

.translate-history {
  background-color: white;
  width: 90%;
  margin-top:30px;

  &-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    padding: 20px;
    background-color: transparent;
    position: relative;

    &::before,
    &::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      height: 1px;
      background-color: #e7e7e7;
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }

    &-left {
      display: flex;
      align-items: center;
      gap: 15px;

      &-btn {
        background-color: white;
        color: $color-primary;
        // border: 1px solid #d9d9d9;
        border-radius: 8px;
        cursor: pointer;
      }

      &-text {
        font-size: 14px;
        color: #555;
      }
    }

    &-right {
      display: flex;
      align-items: center;

      .search-meetings {
        input {
          height: 40px;
          border-radius: 13px 0 0 13px;
          border: none;
          background-color: #f6f6f6;
          &::placeholder {
            font-family: $font;
            font-size: 14px;
            color: #888888;
          }
          &:focus {
            box-shadow: none !important;
          }
        }
        .ant-input-group-addon {
          background-color: #f6f6f6;
          border-radius: 0 13px 13px 0;
          button {
            width: 50px;
            height: 40px;
            background-color: #3b60e4;
            border-radius: 13px !important;
            span {
              color: #fefefe;
            }
          }
        }
      }
    }
  }

  &-body {
    background: #ffffff;
    padding: 10px;
    margin-top: initial;

    &-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      gap: 0;

      .actions-left {
        display: flex;
        gap: 8px;
        align-items: center;
        padding-left: 4px;
      }

      .actions-right {
        display: flex;
        align-items: center;
      }

      .ant-btn {
        width: 40px;
        height: 40px;
        // background: #F6F6F6;
        border: none;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #414141;

        &:hover {
          background: #ececec;
        }
      }

      .ant-picker {
        display: flex;
        border-color: black;
        position: relative;
        .ant-picker-suffix {
          left: calc(100% - 25px);
          top: 50%;
          transform: translateY(-50%);
        }
        &:hover {
          background: #ececec;
        }
      }

      // .ant-checkbox-wrapper {
      //   margin-right: 8px;
      //   font-weight: bold;
      // }
    }

    &-data {
      background: #f4f8f9;
      border-radius: 12px;
      padding: 2px;
      margin-top: 10px;

      .ant-table-wrapper {
        .ant-table {
          background: transparent;
        }

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          text-align: center;
        }

        .ant-table-thead > tr > th:first-child,
        .ant-table-tbody > tr > td:first-child {
          text-align: left;
        }

        .translate-history-row {
          background: #fff !important;
          border-radius: 20px !important;
          box-shadow: none;
          transition: background 0.2s;
          margin: 10px;

          > td {
            background: #fff !important;
            border-bottom: 16px solid #f4f8f9;
            font-size: 15px;
            color: #414141;
            padding: 13px;

            &:first-child {
              border-top-left-radius: 12px !important;
              border-bottom-left-radius: 12px !important;
            }

            &:last-child {
              border-top-right-radius: 12px !important;
              border-bottom-right-radius: 12px !important;
            }
            .translate-history-row > td .action-btn {
              border: none !important;
              background: transparent !important;
              box-shadow: none !important;
              color: #414141;
              border-radius: 4px;
              padding: 0;
              min-width: 32px;
              min-height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: background 0.2s;
            }
          }

          &:last-child > td {
            border-bottom: none;
          }
        }
      }
    }
  }
}

.ant-dropdown-menu {
  .ant-dropdown-menu-item {
    padding: 8px 12px;
  }
}

.translate-history-custom-table {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
  margin-top: 16px;
}

.translate-history-table {
  width: 100%;
  background: #F4F8F9;
  // border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  overflow-x: auto !important;
  margin-top: 16px;
  max-height: 300px;
  margin-bottom: 10px;
  max-width: 100%;
  // min-width removed to allow content to overflow
}

.th-header, .th-row {
  display: flex;
  // align-items: center;
  min-height: 56px;
  padding: 0 16px;
  margin-bottom: 5px;
  min-width: 630px; // Keep this for scroll
}

.th-header {
  display: flex;
  font-weight: 600;
  align-items: center;
  justify-content: center;
  color: #222;
  font-size: 15px;
}

.th-header .th-cell:nth-child(2){
  margin-left: 7px;
  font-weight: bold;
}
.th-header .th-cell:nth-child(3),
.th-header .th-cell:nth-child(4),
.th-header .th-cell:nth-child(5) {
  text-align: center;
  justify-content: center;
  font-weight: bold;
}

.th-row {
  background: #fff;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s;
  margin-bottom: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  border-bottom: none;
  border-radius: 12px;
  &:hover {
    background: #E9EEFF;
  }
}

.th-row .th-cell:nth-child(3),
.th-row .th-cell:nth-child(4),
.th-row .th-cell:nth-child(5) {
  text-align: center;
  justify-content: center;
}

.th-cell {
  flex: 1 1 120px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.th-cell:first-child {
  width: 40px;
  flex: 0 0 40px;
  justify-content: center;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.th-cell:nth-child(2) {
  flex: 2 1 200px;
  font-weight: 500;
  color: #2d3748;
}

.th-actions {
  justify-content: flex-end;
  gap: 8px;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}

.action-btn {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  color: #6b7280 !important;
  font-size: 18px !important;
  padding: 0 6px !important;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-btn:hover {
  color: #1a73e8 !important;
  background: #e8f0fe !important;
}

.th-loading {
  justify-content: center;
  min-height: 80px;
}

// Responsive
@media (max-width: 900px) {
  .th-header, .th-row {
    min-height: 44px;
    padding: 0 4px;
  }
  .th-cell {
    font-size: 13px;
    flex-basis: 80px;
  }
}

@media (max-width: 756px) {
  .translate-history svg {
    width: 16px !important;
    height: 16px !important;
  }
}

.responsive-svg {
  width: 24px;
  height: 24px;
}

@media (max-width: 756px) {
  .responsive-svg {
    width: 16px;
    height: 16px;
  }
  .action-btn {
    padding: 4px 8px;
    font-size: 14px;
  }
}

.translate-history-header-right {
  flex: 1;
  min-width: 180px;
  max-width: 400px;
}

@media (max-width: 756px) {
  .translate-history-header-right {
    width: 100%;
    max-width: 100%;
    margin-top: 8px;
  }
  .search-meetings {
    width: 50% !important;
  }
  .date-range {
    width: 50% !important;
  }
}

.th-action-row {
  display: flex;
  align-items: center;
  gap: 24px;
  justify-content: flex-start;
  padding-left: 8px;
  min-width: 120px;
  margin:20px;
}

.custom-checkbox .ant-checkbox-inner {
  border: 2px solid #747474 !important;
  border-radius: 2px !important;
}



