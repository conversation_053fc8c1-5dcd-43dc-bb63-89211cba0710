/* eslint-disable */
import React, { useEffect, useState } from "react";
import multiDownload from 'multi-download';
import { Button, message, Spin, Upload, Tooltip, Modal } from "antd";
import { FaRegCopy } from "react-icons/fa6";
import { MdDeleteOutline } from "react-icons/md";
import { ReactComponent as Switch } from "../Assets/switch.svg";
import { ReactComponent as Download } from "../Assets/download.svg";
import { ReactComponent as ArrowDown } from "../Assets/arrowdown.svg";
import LanguageDropdown from "./LanguageDropdown";
import { ReactComponent as FileIcon } from "../Assets/file.svg";
import { ReactComponent as DownloadIcon } from "../Assets/downloadicon.svg";
import { ReactComponent as DeleteIcon } from "../Assets/deleteIcon.svg";
import { ReactComponent as AddMore } from "../Assets/addonicon.svg";
import { ReactComponent as Exclamation } from "../Assets/Exclamation.svg";


export default function TranslateBody({
  type,
  isButtonDisabled,
  setIsButtonDisabled,
  translateText,
  setTranslateText,
  sourceLangDropdown,
  targetLangDropdown,
  sourceLanguageDropdownOpen,
  sourceLanguageDropdownClose,
  targetLanguageDropdownOpen,
  targetLanguageDropdownClose,
  languageData,
  documentTranslationLanguageList,
  search,
  setSearch,
  translateTextHandler,
  handleDocumentTranslation,
  isTranslationLoading,
  translationStatus,
  remainingCount,
  errorMsgCount0,
  translatedDocuments,
  setTranslatedDocuments,
  // isSourceAutoDetected,
  resetKey,
  currentSubscription,
}) {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const { Dragger } = Upload;
  const [isDownloadModalVisible, setIsDownloadModalVisible] = useState(false);

  // Get allowed document types from subscription
  const getAllowedDocumentTypes = () => {
    if (!currentSubscription?.Subscription?.SubscriptionDocumentTypes) {
      return ['pdf', 'txt']; // Default fallback
    }
    return currentSubscription.Subscription.SubscriptionDocumentTypes.map(
      (docType) => docType.document_type
    );
  };

  // Get MIME types for allowed document types
  const getMimeTypes = (allowedTypes) => {
    const mimeTypeMap = {
      'pdf': 'application/pdf',
      'txt': 'text/plain',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    };
    return allowedTypes.map(docType => mimeTypeMap[docType]).filter(Boolean);
  };

  // Check if file type is allowed
  const isFileTypeAllowed = (file, allowedTypes) => {
    const allowedMimeTypes = getMimeTypes(allowedTypes);
    const fileExtension = file.name.split('.').pop().toLowerCase();
    
    return allowedTypes.includes(fileExtension) || allowedMimeTypes.includes(file.type);
  };

  // Get formatted file types for display
  const getFormattedFileTypes = (allowedTypes) => {
    return allowedTypes.map(type => type.toUpperCase()).join(', ');
  };

  const allowedDocumentTypes = getAllowedDocumentTypes();
  const acceptAttribute = allowedDocumentTypes.map(docType => `.${docType}`).join(',');

  // Reset internal state when resetKey changes
  useEffect(() => {
    setSelectedFiles([]);
    setIsDownloadModalVisible(false);
  }, [resetKey]);

  const getFileName = (file) => {
    // Prefer .name, fallback to .filename, then .path
    const path = file?.name || file?.filename || file?.path || "";
    // Try to extract from files-... pattern, else just return the name
    const match = path.match(/files-(.*?)\.(pdf|doc|docx|txt)/i);
    if (match) {
      const extractedName = match[1];
      const extension = match[2];
      return `${decodeURIComponent(extractedName)}.${extension}`;
    }
    return path;
  };

  const renderTranslationStatus = () => {
    if (type !== "document") return null;

    if (isTranslationLoading && translatedDocuments.length === 0) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "350px",
            width: "100%",
          }}
        >
          <Spin size="large" />
          <p style={{ marginTop: "20px", fontSize: "16px" }}>
            Translation is in process...
          </p>
        </div>
      );
    }

    // Show file list if there are files, regardless of overall status
    if (Array.isArray(translatedDocuments) && translatedDocuments.length > 0) {
      return (
        <div
          className="selected-files"
          style={{
            display: "flex",
            flexDirection: "column",
            height: "100%",
            width: "100%",
          }}
        >
          <div
            style={{
              flex: 1,
              overflowY: "auto",
              width: "100%",
              borderBottom: translatedDocuments.length > 1 ? "1px solid #c5c5c5" : "none",
            }}
          >
            <div
              style={{
                fontWeight: 600,
                fontSize: 15,
                margin: "1px 0px 15px 0px",
              }}
            >
              {`${translatedDocuments.length}/${translatedDocuments.length} files`}
            </div>
            <ul className="selected-files-list" style={{marginBottom:"10px"}}>
              {[...translatedDocuments].reverse().map((file, idx) => (
                <li
                  className="selected-files-list-item-key"
                  key={file.uid || idx}
                  
                >
                  <div className="selected-files-list-icon">
                    <FileIcon/>
                  </div>
                  <div style={{ flex: 1 }}>
                    <div className="file-name">{getFileName(file)}</div>
                    <div
                      style={{
                        color: "#7E7E7E",
                        fontWeight: 500,
                        fontSize: "12px",
                        marginTop: 0,
                      }}
                    >
                      {file.status === "running" && <span>Translating...</span>}
                      {file.status === "succeeded" && <span>Translated</span>}
                    </div>
                  </div>
                  <div>
                    {file.status === "succeeded" && (
                      <a href={file.path} download>
                        <DownloadIcon />
                      </a>
                    )}
                  </div>
                  <button
                    className="selected-files-list-remove"
                    style={{
                      background: "none",
                      border: "none",
                      cursor: "pointer",
                      padding: 0,
                    }}
                    onClick={() => {
                      const reversed = [...translatedDocuments].reverse();
                      const newFiles = reversed.filter((_, i) => i !== idx);
                      setTranslatedDocuments(newFiles.reverse());
                    }}
                  >
                    <MdDeleteOutline
                      style={{ width: 24, height: 24, color: "#636363" }}
                    />
                  </button>
                </li>
              ))}
            </ul>
          </div>
          {/* Download all button in a separate div with border top */}
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              paddingTop: "4px",
              marginTop: "0",
            }}
          >
            {translatedDocuments.length > 1 && (
              <Button
                type="primary"
                className="translate-button"
                onClick={() => setIsDownloadModalVisible(true)}
                disabled={
                  !(
                    translatedDocuments.length > 1 &&
                    translatedDocuments.every((f) => f.status === "succeeded")
                  )
                }
              >
                Download all
              </Button>
            )}
          </div>
        </div>
      );
    }

    // If translation failed and no files, show error
    if (translationStatus === "failed") {
      return (
        <div className="translation-status error">
          Translation failed. Please try again.
        </div>
      );
    }

    return null;
  };

  // Swap source and target language (and text fields for text translation)
  const handleLanguageSwitch = () => {
    setTranslateText((prev) => ({
      ...prev,
      source: prev.target,
      target: prev.source,
      ...(type === "text"
        ? {
            text: prev.translatedText,
            translatedText: prev.text,
          }
        : {}),
    }));
  };

  const props = {
    name: "file",
    multiple: true,
    // action: "https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",
    accept: acceptAttribute,
    fileList: selectedFiles,
    beforeUpload(file, fileList) {
      const totalFiles = selectedFiles.length + fileList.length;
      if (totalFiles > 10) {
        message.error("Only 10 files are allowed to translate");
        return Upload.LIST_IGNORE;
      }
      // Check each file for size and type
      // If any file > 25MB, show error and prevent upload
      // Check each file in fileList (multiple selection)
      for (let currentFile of fileList) {
        if (currentFile.size > 25 * 1024 * 1024) {
          message.error(`File "${currentFile.name}" size is more than 25 MB`);
          return Upload.LIST_IGNORE;
        }
        if (!isFileTypeAllowed(currentFile, allowedDocumentTypes)) {
          message.error(`File "${currentFile.name}" is not an allowed type. You can only upload ${getFormattedFileTypes(allowedDocumentTypes)} files!`);
          return Upload.LIST_IGNORE;
        }
      }
      if (file && fileList.length === 1) {
        if (file.size > 25 * 1024 * 1024) {
          message.error(`File "${file.name}" size is more than 25 MB`);
          return Upload.LIST_IGNORE;
        }
        if (!isFileTypeAllowed(file, allowedDocumentTypes)) {
          message.error(`File "${file.name}" is not an allowed type. You can only upload ${getFormattedFileTypes(allowedDocumentTypes)} files!`);
          return Upload.LIST_IGNORE;
        }
        // Add the single file if not already in selectedFiles and limit not exceeded
        if (selectedFiles.length < 10) {
          setSelectedFiles([...selectedFiles, file]);
          setIsButtonDisabled(false);
        }
        return false; // Prevent auto-upload
      }
      // if (hasError) {
      //   message.error("Uploaded file size more than 25 MB");
      //   return Upload.LIST_IGNORE;
      // }
      // Add new files to selectedFiles, but do not exceed 10
      const filesToAdd = fileList.slice(0, 10 - selectedFiles.length);
      setSelectedFiles([...selectedFiles, ...filesToAdd]);
      setIsButtonDisabled(false);
      return false; // Prevent auto-upload
    },
    onRemove(file) {
      setSelectedFiles((prev) => prev.filter((f) => f.uid !== file.uid));
      if (selectedFiles.length <= 1) {
        setIsButtonDisabled(true);
      }
    },
    showUploadList: false,
    // onDrop(e) {
    //   // No-op, handled by beforeUpload
    // },
  };

  useEffect(() => {
    if (translateText.target.code_alpha_1 && translateText.text.length > 0) {
      setIsButtonDisabled(false);
    } else {
      setIsButtonDisabled(true);
    }
  }, [translateText.target.code_alpha_1]);


  return (
    <div className="translate-body">
      <div className="translate-body-header">
        <Tooltip
          title="Choose a source language of the original files"
          placement="top"
          color="#535353"
          overlayStyle={{ whiteSpace: "nowrap", maxWidth: "none" }}
        >
          <div className="translate-body-header-source">
            <span>*</span>
            Source Language
            {translateText.source ? (
              <div className="translate-body-header-source-language-name">
                {type === "document"
                  ? translateText.source.language
                  : translateText.source.codeName}
              </div>
            ) : (
              <div
                className="translate-body-header-source-language-name"
                style={{ color: "#3B60E4" }}
              >
                Auto Detection
              </div>
            )}
            <ArrowDown
              className="translate-body-header-arrowdown"
              onClick={sourceLanguageDropdownOpen}
              style={{ cursor: "pointer" }}
            />
          </div>
        </Tooltip>
        <Switch className="translate-body-header-switch" style={{ cursor: "pointer" }} onClick={handleLanguageSwitch} />
        <Tooltip
          title="Choose a target language for the translation"
          placement="top"
          color="#535353"
          overlayStyle={{ whiteSpace: "nowrap", maxWidth: "none" }}
        >
          <div className="translate-body-target-language">
            <span>*</span>
            Target Language
            {translateText.target && (
              <div className="translate-body-header-source-language-name">
                {type === "document"
                  ? translateText.target.language
                  : translateText.target.codeName}
              </div>
            )}
            <ArrowDown
              className="translate-body-header-arrowdown"
              onClick={targetLanguageDropdownOpen}
              style={{ cursor: "pointer" }}
            />
          </div>
        </Tooltip>
        {sourceLangDropdown && (
          <LanguageDropdown
            type="source"
            translationType={type}
            languageData={
              type === "document"
                ? documentTranslationLanguageList
                : languageData
            }
            search={search}
            setSearch={setSearch}
            language={translateText}
            setLanguage={setTranslateText}
            closeDropdown={sourceLanguageDropdownClose}
          />
        )}
        {targetLangDropdown && (
          <LanguageDropdown
            type="target"
            translationType={type}
            languageData={
              type === "document"
                ? documentTranslationLanguageList
                : languageData
            }
            search={search}
            setSearch={setSearch}
            language={translateText}
            setLanguage={setTranslateText}
            closeDropdown={targetLanguageDropdownClose}
          />
        )}
      </div>
      <div className="translate-body-content">
        <div className="translate-body-content-input">
          {type === "text" && (
            <textarea
              placeholder="Enter text to translate"
              rows={10}
              onChange={(e) => {
                setTranslateText({ ...translateText, text: e.target.value });
                if (translateText.target.code_alpha_1) {
                  if (e.target.value.length > 0) {
                    setIsButtonDisabled(false);
                  }
                } else {
                  setIsButtonDisabled(true);
                }
              }}
            />
          )}
          {type === "document" && (
            <div
              className={`translate-body-content-input-document ${
                selectedFiles.length > 0 ? "active" : ""
              }`}
            >
              {/* {selectedFiles.length === 0 && ( */}
              {selectedFiles.length === 0 && (
                <Dragger
                  {...props}
                  className="translate-body-content-input-document-box"
                >
                  <p className="ant-upload-drag-icon">
                    <Download className="translate-body-content-input-document-icon" />
                  </p>
                  <p className="ant-upload-text">
                    Drop your file here, Or{" "}
                    <span className="browse-link">Browse</span>
                  </p>
                  {selectedFiles.length === 0 && (
                    <p className="ant-upload-hint">
                      Supported file types: {getFormattedFileTypes(allowedDocumentTypes)} less than
                      25MB.
                      <span style={{ fontWeight: "bold" }}>
                        You can upload upto 10 files total.
                      </span>
                    </p>
                  )}
                </Dragger>
              )}
              {/* )} */}
              {selectedFiles.length > 0 && (
                <div className="selected-files">
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      margin: "-4px 0px 15px 0px",
                    }}
                  >
                    <span style={{ fontWeight: 600, fontSize: 15 }}>
                      {`${selectedFiles.length}/${selectedFiles.length} files uploaded`}
                    </span>
                    {selectedFiles.length < 10 && (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <input
                          type="file"
                          id="add-more-files"
                          multiple
                          accept={acceptAttribute}
                          style={{ display: "none" }}
                          onChange={(e) => {
                            const files = Array.from(e.target.files);
                            const totalFiles = selectedFiles.length + files.length;
                            if (totalFiles > 10) {
                              // Show error only once, not per file
                              message.error("Only 10 files are allowed to translate");
                              e.target.value = ""; // Reset input
                              return;
                            }
                            // Check all files for type, but only show error once if any is invalid
                            const invalidFile = files.find(
                              (currentFile) => !isFileTypeAllowed(currentFile, allowedDocumentTypes)
                            );
                            if (invalidFile) {
                              message.error(`You can only upload ${getFormattedFileTypes(allowedDocumentTypes)} files!`);
                              e.target.value = ""; // Reset input
                              return;
                            }
                            setSelectedFiles([...selectedFiles, ...files]);
                            setIsButtonDisabled(false);
                            e.target.value = ""; // Reset input
                          }}
                        />
                        <Button
                          type="link"
                          icon={<AddMore style={{ marginRight: 8 }} />}
                          onClick={() =>
                            document.getElementById("add-more-files").click()
                          }
                          style={{
                            fontWeight: 600,
                            fontSize: "15px",
                            color: "#4A4A4A",
                          }}
                          disabled={selectedFiles.length >= 10}
                        >
                          Add More files
                        </Button>
                      </div>
                    )}
                    {selectedFiles.length >= 10 && (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <input
                          type="file"
                          id="add-more-files"
                          multiple
                          accept={acceptAttribute}
                          style={{ display: "none" }}
                          disabled
                        />
                        <Tooltip title="You can upload maximum 10 files">
                          <Button
                            type="link"
                            icon={<AddMore style={{ marginRight: 8 }} />}
                            style={{
                              fontWeight: 600,
                              fontSize: "15px",
                              color: "#4A4A4A",
                            }}
                            disabled
                          >
                            Add More files
                          </Button>
                        </Tooltip>
                      </div>
                    )}
                  </div>
                  <ul className="selected-files-list">
                    {selectedFiles.map((file, index) => (
                      <li key={index} className="selected-files-list-item">
                        <div className="selected-files-list-icon">
                          <FileIcon />
                        </div>
                        <div
                          style={{ display: "flex", flexDirection: "column" }}
                        >
                          <span className="file-name-translated">
                            {getFileName(file)}
                          </span>
                          <span
                            style={{
                              fontSize: 12,
                              color: "#7E7E7E",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            {(file.size / (1024 * 1024)).toFixed(2)} MB
                            <span
                              className="file-status"
                              style={{
                                marginLeft: 8,
                                color:
                                  file.status === "notstarted"
                                    ? "#3B60E4"
                                    : "#7E7E7E",
                                fontWeight: 500,
                              }}
                            >
                              {file.status === "notstarted"
                                ? "Uploading"
                                : "Uploaded"}
                            </span>
                          </span>
                        </div>
                        <Button
                          type="text"
                          danger
                          className="selected-files-list-remove"
                          onClick={() => {
                            const newFiles = selectedFiles.filter(
                              (_, i) => i !== index
                            );
                            setSelectedFiles(newFiles);
                            if (newFiles.length === 0) {
                              setIsButtonDisabled(true);
                            }
                          }}
                        >
                          <DeleteIcon className="fs-5" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Common Button for text and document */}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "12px",
              marginTop: "0.8rem",
            }}
          >
            <Button
              type="primary"
              className="translate-button"
              disabled={
                isButtonDisabled || isTranslationLoading || remainingCount <= 0
              }
              onClick={() => {
                if (remainingCount <= 0) {
                  if (errorMsgCount0) errorMsgCount0();
                  return;
                }
                if (type === "text") {
                  if (
                    translateText.source.code_alpha_1 ===
                    translateText.target.code_alpha_1
                  ) {
                    message.warning(
                      "Source and target language cannot be the same."
                    );
                    return;
                  } else if (translateText.target.code_alpha_1 === "") {
                    message.warning("Please select target language.");
                    return;
                  }
                  translateTextHandler();
                }
                if (type === "document") {
                  // Reset translatedDocuments to show all selected files as processing
                  setTranslatedDocuments(
                    selectedFiles.map(file => ({
                      ...file,
                      name: file.name || file.filename || file.path || "Unnamed file",
                      status: "running"
                    }))
                  );
                  handleDocumentTranslation(
                    selectedFiles,
                    translateText.target?.language_code
                  );
                }
              }}
            >
              {isTranslationLoading ? "Translate" : "Translate"}
            </Button>
          </div>
        </div>
        <div className="translate-body-content-output">
          {type === "text" ? (
            <div
              className={`translate-body-content-output-text ${
                translateText.translatedText ? "active" : ""
              }`}
              style={{ whiteSpace: "pre-wrap" }}
            >
              {translateText.translatedText}
            </div>
          ) : (
            renderTranslationStatus()
          )}
          {type === "text" && (
            <div className="translate-body-content-output-action">
              <FaRegCopy style={{ marginTop: "15px" }}
                onClick={() => {
                  if (translateText.translatedText) {
                    navigator.clipboard.writeText(translateText.translatedText);
                    message.success("Text copied to clipboard.");
                  } else {
                    message.warning("No text to copy.");
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>
      <Modal
        title={null}
        open={isDownloadModalVisible}
        onCancel={() => setIsDownloadModalVisible(false)}
        footer={null}
        centered
        closable={false}
        bodyStyle={{ textAlign: "center", padding: "25px 20px"}}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 10 }}><Exclamation/></div>
          <div style={{ fontWeight: 500, fontSize: 16 }}>
            Are you sure you want to download all the files?
          </div>
        </div>
        <div style={{ display: "flex", justifyContent: "center", gap: 16 }}>
          <Button
            type="primary"
            style={{ minWidth: 80 ,border: "1px solid #3B60E4" ,borderRadius:7}}
            onClick={() => {
              // Download all succeeded files
              const urls = translatedDocuments
                .filter(f => f.status === "succeeded")
                .map(f => f.path);
              if (urls.length > 0) {
                multiDownload(urls);
              }
              setIsDownloadModalVisible(false);
            }}
          >
            Yes
          </Button>
          <Button
            style={{ minWidth: 80 , border: "1px solid #3B60E4" ,borderRadius:7}}
            onClick={() => setIsDownloadModalVisible(false)}
          >
            No
          </Button>
        </div>
      </Modal>
    </div>
  );
}
