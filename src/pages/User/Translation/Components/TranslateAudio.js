import { Button, message, Modal, Pagination, Select, Upload } from "antd";
import "../Translation2.scss";
import { useFormik } from "formik";
import { useState } from "react";
import { UploadOutlined } from "@ant-design/icons";
import { FiRefreshCw } from "react-icons/fi";
import { BsSoundwave } from "react-icons/bs";
import { TranslateServices } from "../../../../services";
import { modalNotification } from "../../../../utils";

export default function TranslateAudio({
  languageList,
  currentSubscription,
}) {
  const [file, setFile] = useState(null);
  const [isAudioTranslationModalOpen, setIsAudioTranslationModalOpen] = useState(false);
  const [audioTranslationApiCalled, setAudioTranslationApiCalled] = useState(false);
  const [jobsList, setJobsList] = useState([]);

  // Get allowed media types from subscription
  const getAllowedMediaTypes = () => {
    if (!currentSubscription?.Subscription?.SubscriptionMediaTypes) {
      return ['mp3', 'mp4']; // Default fallback
    }
    return currentSubscription.Subscription.SubscriptionMediaTypes.map(
      (mediaType) => mediaType.media_type
    );
  };

  // Get MIME types for allowed media types
  const getMimeTypes = (allowedTypes) => {
    const mimeTypeMap = {
      'mp3': 'audio/mpeg',
      'mp4': 'video/mp4',
      'wav': 'audio/wav',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      'wmv': 'video/x-ms-wmv'
    };
    return allowedTypes.map(type => mimeTypeMap[type]).filter(Boolean);
  };

  // Check if file type is allowed
  const isFileTypeAllowed = (fileAudio, allowedTypes) => {
    const allowedMimeTypes = getMimeTypes(allowedTypes);
    const fileExtension = fileAudio.name.split('.').pop().toLowerCase();
    
    return allowedTypes.includes(fileExtension) || allowedMimeTypes.includes(file.type);
  };

  // Get formatted media types for display
  const getFormattedMediaTypes = (allowedTypes) => {
    return allowedTypes.map(type => `.${type.toUpperCase()}`).join(', ');
  };

  const allowedMediaTypes = getAllowedMediaTypes();
  const acceptAttribute = allowedMediaTypes.map(type => `.${type}`).join(', ');

  const jobsStatusList = [
    {
      label: "Queued",
      value: "queued",
    },
    {
      label: "In Progress",
      value: "in_progress",
    },
    {
      label: "Final Processing",
      value: "final_processing",
    },
    {
      label: "Completed",
      value: "completed",
    },
    {
      label: "Failed",
      value: "failed",
    },
  ];
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const audioTranslationData = useFormik({
    initialValues: {
      sourceLanguage: "hindi",
      targetLanguage: "english",
      type: "audio",
      gender: "male",
    },
    onSubmit: () => {
      audioTranslationData.resetForm();
    },
  });
  const voiceList = [
    {
      label: "Male",
      value: "male",
    },
    {
      label: "Female",
      value: "female",
    },
  ];

  

  const props = {
    name: "file",
    multiple: false,
    accept: acceptAttribute,
    iconRender: (uploadFile) => {
      return uploadFile.status === "done" ? <BsSoundwave /> : <BsSoundwave />;
    },
    onChange(info) {
      const { status } = info.file;
      // if (status !== "uploading") {
      //   console.log(info.file, info.fileList);
      // }
      if (status === "done") {
        message.success(`${info.file.name} file uploaded successfully.`);
      } else if (status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    beforeUpload: (uploadFile) => {
      // Validate file type against subscription
      if (!isFileTypeAllowed(uploadFile, allowedMediaTypes)) {
        message.error(`You can only upload ${getFormattedMediaTypes(allowedMediaTypes)} files!`);
        return false;
      }
      setFile(uploadFile);
      return false;
    },
    onRemove: () => {
      setFile(null);
    },
  };

  const getJobsList = async (pageNo=1) => {
    try {
      const response = await TranslateServices.getJobsListService({
        page: pageNo,
      });
      const { success, data } = response;
      if (success === true) {
        setJobsList(data?.jobs);
        setTotalPages(data?.total_pages);
      } else {
        modalNotification({
          type: "error",
          message: "Something went wrong",
        });
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const goToPage = (pageNo) => {
    setPage(pageNo);
    getJobsList(pageNo);
  };

  const handleUpload = async () => {
    setAudioTranslationApiCalled(true);
    audioTranslationData.resetForm();
    if (!file) {
      alert("Please select a file first!");
      return;
    }
    const formData = new FormData();
    formData.append("file", file); // Attach the file
    formData.append(
      "source_language",
      audioTranslationData.values.sourceLanguage
    );
    formData.append(
      "target_language",
      audioTranslationData.values.targetLanguage
    );
    formData.append("type", "audio");
    formData.append("gender", audioTranslationData.values.gender);
    try {
      const response = await TranslateServices.createAudioTranslationJobService(
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      const { success } = response;
      if (success === true) {
        setFile(null);
        getJobsList();
        props.onRemove(file);
        setAudioTranslationApiCalled(false);
      } else {
        alert("File upload failed!");
        setAudioTranslationApiCalled(false);
        setFile(null);
        props.onRemove(file);
      }
    } catch (error) {
      alert("File upload failed!");
      setAudioTranslationApiCalled(false);
      setFile(null);
      props.onRemove(file);
    } finally {
      setFile(null);
      props.onRemove(file);
      setAudioTranslationApiCalled(false);
    }
  };
  return (
    <div className="translateBar bg-white">
      <div
        className={`${
          file ? "translateBar-select-not" : "translateBar-select-file"
        }`}
      >
        {/* Select File of MP3 Format */}
        <Upload
          {...props}
          className={`upload-btn ${file ? "" : "no-file"}`}
          fileList={file ? [file] : []}
        >
          {file ? null : (
            <Button icon={<UploadOutlined />}>Select a file</Button>
          )}
        </Upload>
        {file && (
          <>
            <FiRefreshCw />
          </>
        )}
        {file && (
          <>
            {audioTranslationApiCalled ? (
              <div className="audio-translation-loader">
                {/* map 10 span here */}
                {[...Array(8)].map((_, index) => (
                  <span key={index} />
                ))}
              </div>
            ) : (
              <Button
                type="primary"
                onClick={() => {
                  setIsAudioTranslationModalOpen(true);
                }}
              >
                Translate
              </Button>
            )}
          </>
        )}
      </div>
      <div className="jobs-list">
        <table className="jobs-table">
          <thead>
            <tr className="jobs-header">
              <th>Audio</th>
              <th>Status</th>
              <th>Source Language</th>
              <th>Target Language</th>
              <th>Voice</th>
              <th>Output</th>
            </tr>
          </thead>
          <tbody>
            {jobsList.map((item, key) => (
              <tr key={key} className="jobs-item">
                <td className="jobs-item-title">Audio {key + 1}</td>
                <td className={`${item?.status}`}>
                  <div>
                    {jobsStatusList.map((status) => {
                      if (status.value === item?.status) {
                        return (
                          <span
                            key={status.value}
                            className={`status-${status.value}`}
                          >
                            {status.label}
                          </span>
                        );
                      }
                      return null;
                    })}
                  </div>
                </td>
                <td className="jobs-item-source">{item?.source_language}</td>
                <td className="jobs-item-target">{item?.target_language}</td>
                <td className="jobs-item-voice">{item?.gender}</td>
                <td className="jobs-item-output">
                  {item?.status === "completed" ? (
                    <a
                      href={item?.output_file_path}
                      download
                      target="_blank"
                      rel="noreferrer"
                    >
                      Download
                    </a>
                  ) : item?.status === "failed" ? (
                    <span>Failed</span>
                  ) : (
                    <span>Processing</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {jobsList.length > 0 && (
          <div className="mt-3 w-75 d-flex justify-content-center">
            <Pagination
              count={jobsList.length}
              page={page}
              sizePerPage={10}
              noOfPage={totalPages}
              goToPage={goToPage}
            />
          </div>
        )}
      </div>
      <Modal
        title="Audio Translation"
        open={isAudioTranslationModalOpen}
        onOk={() => {
          handleUpload();
          setIsAudioTranslationModalOpen(false);
        }}
        onCancel={() => setIsAudioTranslationModalOpen(false)}
        className="audio-translation-modal"
      >
        <div className="audio-translation-box">
          <label>Source Language</label> :{" "}
          <Select
            placeholder="Select Source Language"
            options={languageList}
            value={audioTranslationData.values.sourceLanguage}
            onChange={(e) => {
              audioTranslationData.setFieldValue("sourceLanguage", e);
            }}
            style={{ width: "200px" }}
          />
        </div>
        <div className="audio-translation-box">
          <label>Target Language</label> :{" "}
          <Select
            placeholder="Select Target Language"
            options={languageList}
            value={audioTranslationData.values.targetLanguage}
            onChange={(e) =>
              audioTranslationData.setFieldValue("targetLanguage", e)
            }
            style={{ width: "200px" }}
          />
        </div>
        <div className="audio-translation-box">
          <label>Voice</label> :{" "}
          <Select
            placeholder="Select Voice"
            options={voiceList}
            value={audioTranslationData.values.gender}
            onChange={(e) => audioTranslationData.setFieldValue("gender", e)}
            style={{ width: "200px" }}
          />
        </div>
        {/* <Button type="primary" onClick={handleUpload}>Translate</Button> */}
      </Modal>
      {/* {fromLangDropdown && (
        <LanguageDropdown
          langDropdownClose={fromLangDropdownClose}
          setLanguage={onFromLanguageChange}
          languageData={languageList}
          // autoDetectLanguage={autoDetectLanguage}
          // search={search}
          // setSearch={setSearch}
          // defaultKey={defaultKey}
        />
      )}
      {toLangDropdown && (
        <LanguageDropdown
          langDropdownClose={toLangDropdownClose}
          setLanguage={setToLanguage}
          languageData={languageList}
          // autoDetectLanguage={autoDetectLanguage}
          // search={search}
          // setSearch={setSearch}
          // defaultKey={defaultKey}
        />
      )} */}
    </div>
  );
}
