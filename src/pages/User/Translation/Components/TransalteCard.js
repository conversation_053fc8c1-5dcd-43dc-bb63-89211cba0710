import React from "react";

export default function TransalteCard ({ title, des, image: SvgComponent, onClick, isActive, isDisabled }) {
  return (
    <div
      className={`translate-card${isActive ? " active" : ""}${isDisabled ? " disabled" : ""}`}
      onClick={isDisabled ? undefined : onClick}
      style={isDisabled ? { cursor: "not-allowed", opacity: 0.5, pointerEvents: "auto" } : {}}
    >
      <SvgComponent className="translate-card-icon" />
      <div className="translate-card-content">
        <span className="translate-card-title">{title}</span>
        <p className="translate-card-subtitle">{des}</p>
      </div>
    </div>
  );
}
