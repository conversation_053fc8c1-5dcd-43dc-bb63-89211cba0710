import { Input } from "antd";
import React from "react";
import {ReactComponent as SearchIcon} from  "../Assets/search icon.svg"
import {ReactComponent as Close} from  "../Assets/Close.svg"

export default function LanguageDropdown({
  type,
  translationType,
  languageData,
  search,
  setSearch,
  language,
  setLanguage,
  closeDropdown,
}) {
  const filteredLanguageData = languageData.filter((item) => {
    if(translationType === "document"){
      const searchValue = search.toLowerCase();
      return item.language.toLowerCase().includes(searchValue);
    }
    const searchValue = search.toLowerCase();
    return item.codeName.toLowerCase().includes(searchValue);
  });

  return (
    <div className="language-dropdown">
      <Input
        prefix={<SearchIcon />}
        suffix={<Close onClick={closeDropdown} style={{ cursor: 'pointer' }} />}
        placeholder="Search language"
        allowClear
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        className="language-dropdown-input"
      />
      <div className="language-dropdown-list">
        {filteredLanguageData.map((item) => (
          <div
            key={translationType === "document" ? item.language_code : item.code_alpha_1}
            className="language-dropdown-list-item"
            onClick={() => {
              setLanguage({ ...language, [type]: item });
              closeDropdown();
            }}
          >
            {translationType === "document" ? item.language : item.codeName}
          </div>
        ))}
      </div>
    </div>
  );
}
