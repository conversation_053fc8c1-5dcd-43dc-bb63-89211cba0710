import React, { useState, useEffect, useRef } from "react";
import {
  Input,
  Button,
  Dropdown,
  // Menu,
  Spin,
  DatePicker,
  Checkbox,
} from "antd";
import multiDownload from 'multi-download';
import { ShareAltOutlined } from "@ant-design/icons";
import moment from "moment";
import { ReactComponent as ArrowDown } from "../Assets/Chevron.svg";
import { ReactComponent as FileIcon } from "../Assets/document.svg";
import { TranslateServices } from "../../../../services";
import { modalNotification } from "../../../../utils";
import { ReactComponent as ArrowRight } from "../Assets/arrowright.svg";
import { ReactComponent as ArrowLeft } from "../Assets/arrowleft.svg";
import { ReactComponent as DownloadIcon } from "../Assets/downloadicon.svg";
import { ReactComponent as Delete } from "../Assets/delete.svg";
import "./TranslateHistory.scss";

// import {ReactComponent as SearchIcon} from "../Assets/search icon.svg"

const { Search } = Input;

const getLanguageName = (code, languages = []) => {
  if (!code || languages.length === 0) {
    return code;
  }
  const language = languages.find((lang) => lang.language_code === code);
  return language ? language.language : code;
};

const formatFileName = (fileName) => {
  if (!fileName) return "";
  const name = fileName.split("files-")[1];
  if (!name) return fileName;

  // Find the last occurrence of underscore followed by language code and extension
  const match = name.match(/^(.+)_([^_]+\.[^_]+)$/);
  if (!match) return name;

  const [, baseName, extension] = match;
  return baseName.length > 15
    ? `${baseName.substring(0, 15)}...${extension}`
    : `${baseName}_${extension}`;
};

export default function TranslateHistory({ 
  documentTranslationLanguageList,
  getTranslatedDocument
}) {
  const [history, setHistory] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [filterType, setFilterType] = useState("all"); // filter state
  const [dateRange, setDateRange] = useState([null, null]); // NEW: date range state
  const [actionRowChecked, setActionRowChecked] = useState(false); // <-- Added
  const [actionRowIndeterminate, setActionRowIndeterminate] = useState(false); // <-- Added
  const pageSize = 8;
  // const [dateRange, setDateRange] = useState([moment(), moment()]);
  const { RangePicker } = DatePicker;
  const pollingJobIdsRef = useRef([]); // Track jobIds being polled
  const pollingIntervalRef = useRef(null);
  const isPollingRef = useRef(false); // Track if polling is running

  const fetchHistory = async (searchTerm = "") => {
    setLoading(true);
    try {
      let response;
      if (searchTerm && searchTerm.trim() !== "") {
        response = await TranslateServices.searchDocumentService(searchTerm);
        if (response.success === 1 && Array.isArray(response.data?.documents)) {
          setHistory(response.data?.documents);
        } else {
          setHistory([]);
          modalNotification({
            type: "error",
            message: response.message || "No documents found for your search.",
          });
        }
      } else {
        response = await TranslateServices.getHistoryService();
        if (response.success === 1 && Array.isArray(response.data?.documents)) {
          setHistory(response.data?.documents);
        } else {
          setHistory([]);
          modalNotification({
            type: "error",
            message: "Failed to load translation history.",
          });
        }
      }
    } catch (error) {
      modalNotification({
        type: "error",
        message: "Something went wrong while fetching history.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = (url) => {
    if (url) {
      window.open(url, "_blank");
    } else {
      modalNotification({
        type: "error",
        message: "Download link is not available for this item.",
      });
    }
  };

  const handleShare = (url) => {
    if (url) {
      navigator.clipboard.writeText(url)
        .then(() => {
          modalNotification({
            type: "success",
            message: "Download link copied to clipboard!",
          });
        })
        .catch(() => {
          modalNotification({
            type: "error",
            message: "Failed to copy link to clipboard.",
          });
        });
    } else {
      modalNotification({
        type: "error",
        message: "Download link is not available for this item.",
      });
    }
  };

  const handleDelete = async () => {
    if (selectedRows.length === 0) {
      modalNotification({
        type: "warning",
        message: "Please select at least one document to delete.",
      });
      return;
    }
    setLoading(true);
    try {
      const response = await TranslateServices.deleteDocumentService(selectedRows.map(row => row.id));
      if (response.success === 1) {
        modalNotification({
          type: "success",
          message: "Selected document(s) deleted successfully.",
        });
        setSelectedRows([]);
        fetchHistory();
      } else {
        modalNotification({
          type: "error",
          message: response.message || "Failed to delete document(s).",
        });
      }
    } catch (error) {
      modalNotification({
        type: "error",
        message: "Something went wrong while deleting document(s).",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadSelected = () => {
    if (selectedRows.length === 0) {
      modalNotification({
        type: "warning",
        message: "Please select at least one document to download.",
      });
      return;
    }
    const selectedItems = history.filter(item =>
      selectedRows.some(row => row.id === item.id)
    );
    const urls = selectedItems
      .map(item => item.downloadUrl)
      .filter(Boolean);
    if (urls.length === 0) {
      modalNotification({
        type: "error",
        message: "No valid download links found for selected items.",
      });
      return;
    }
    multiDownload(urls);
  };

  useEffect(() => {
    fetchHistory(searchValue);
  }, [searchValue]);



  // NEW: filter logic
  const filterHistory = (historyList, type, range) => {
    // If range is set, filter by date range
    if (range && range[0] && range[1]) {
      const [start, end] = range;
      return historyList.filter(item => {
        // Parse created_at as 'YYYY-MM-DD', start/end as 'DD-MM-YYYY'
        const created = moment(item.created_at, 'YYYY-MM-DD');
        const startDate = moment(start, 'DD-MM-YYYY');
        const endDate = moment(end, 'DD-MM-YYYY');
        return created.isSameOrAfter(startDate.startOf('day')) && created.isSameOrBefore(endDate.endOf('day'));
      });
    }
    if (type === "last7") {
      const sevenDaysAgo = moment().subtract(7, 'days').startOf('day');
      return historyList.filter(item => moment(item.created_at, 'YYYY-MM-DD').isSameOrAfter(sevenDaysAgo));
    }
    if (type === "last30") {
      const thirtyDaysAgo = moment().subtract(30, 'days').startOf('day');
      return historyList.filter(item => moment(item.created_at, 'YYYY-MM-DD').isSameOrAfter(thirtyDaysAgo));
    }
    return historyList;
  };

  // Update filterMenu to set filterType
  const filterMenuItems = [
    {
      key: 'all',
      label: 'All',
      onClick: () => {
        setFilterType('all');
        setDateRange([null, null]);
        setCurrentPage(1);
      }
    },
    {
      key: 'last7',
      label: 'Last 7 days',
      onClick: () => {
        setFilterType('last7');
        setDateRange([moment().subtract(6, 'days'), moment()]);
        setCurrentPage(1);
      }
    },
    {
      key: 'last30',
      label: 'Last 30 days',
      onClick: () => {
        setFilterType('last30');
        setDateRange([moment().subtract(29, 'days'), moment()]);
        setCurrentPage(1);
      }
    }
  ];

  const handleNextPage = () => {
    if (currentPage * pageSize < history.length) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Filtered and paginated history
  const filteredHistory = filterHistory(history, filterType, dateRange);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedHistory = filteredHistory.slice(startIndex, startIndex + pageSize);
  const displayStartItem = filteredHistory.length > 0 ? startIndex + 1 : 0;
  const displayEndItem = Math.min(startIndex + pageSize, filteredHistory.length);

  // Helper to get unique job ids with status not succeeded
  const getUniqueJobIdsInProgress = (historyList) => {
    return [...new Set(historyList.filter(item => item.status !== "succeeded").map(item => item.job_id))];
  };

  // Polling effect (only start/stop when jobIds in progress change)
  useEffect(() => {
    const uniqueJobIds = getUniqueJobIdsInProgress(history);
    pollingJobIdsRef.current = uniqueJobIds;

    // If there are jobs to poll and polling is not running, start polling
    if (uniqueJobIds.length > 0 && !isPollingRef.current) {
      isPollingRef.current = true;
      const pollJobs = async () => {
        const jobIdsToPoll = [...pollingJobIdsRef.current];
        await Promise.all(jobIdsToPoll.map(async (jobId) => {
          try {
            const result = await getTranslatedDocument({ batchId: jobId });
            if (result && result.success === 1 && result.data && result.data.summary) {
              const { summary, documents } = result.data;
              
              // Update status for all documents in this job, regardless of inProgress status
              setHistory(prevHistory => prevHistory.map(item => {
                if (item.job_id === jobId) {
                  // Try multiple ways to match the document
                  const updatedDoc = documents.find(doc => {
                    // Match by target_file_id (exact match)
                    if (doc.status) {
                      return true;
                    }
                    return false;
                  });

                  if (updatedDoc) {
                    return {
                      ...item,
                      status: updatedDoc.status || item.status,
                      statusDescription: updatedDoc.statusDescription || item.statusDescription,
                      downloadUrl: updatedDoc.path || updatedDoc.downloadUrl || item.downloadUrl,
                    };
                  }
                }
                return item;
              }));
              
              // Only remove from polling when all documents in this job are complete
              if (summary.inProgress === 0) {
                pollingJobIdsRef.current = pollingJobIdsRef.current.filter(id => id !== jobId);
              }
            }
          } catch (e) {
            // Silently ignore errors during polling
          }
        }));
        // If all jobs are done, stop polling
        if (pollingJobIdsRef.current.length === 0) {
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          isPollingRef.current = false;
        }
      };
      // Start interval
      pollingIntervalRef.current = setInterval(pollJobs, 10000);
      // Run immediately
      pollJobs();
    }
    // If there are no jobs to poll, stop polling
    if (uniqueJobIds.length === 0 && isPollingRef.current) {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      isPollingRef.current = false;
    }
    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      isPollingRef.current = false;
    };
  }, [JSON.stringify(getUniqueJobIdsInProgress(history)), getTranslatedDocument]);

  return (
    <div className="translate-history">
      <div className="translate-history-header">
        <div className="translate-history-header-left">
            <span className="translate-history-header-left-btn">
              Translated documents
            </span>
          <Dropdown menu={{ items: filterMenuItems }} trigger={["click"]}>
            <span className="translate-history-header-left-btn">
              {/* Show selected filter label */}
              {filterType === 'all' && 'All'}
              {filterType === 'last7' && 'Last 7 days'}
              {filterType === 'last30' && 'Last 30 days'}
              <ArrowDown style={{ marginLeft: "8px" }} />
            </span>
          </Dropdown>
          <span className="translate-history-header-left-text">
            {displayStartItem}-{displayEndItem} of {filteredHistory.length}
          </span>
          <span>
            <ArrowLeft
              style={{
                marginRight: "25px",
                cursor: currentPage === 1 ? "not-allowed" : "pointer",
              }}
              onClick={handlePrevPage}
            />
            <ArrowRight
              style={{
                cursor:
                  displayEndItem >= filteredHistory.length ? "not-allowed" : "pointer",
              }}
              onClick={handleNextPage}
            />
          </span>
        </div>
        <div className="translate-history-header-right">
          <Search
            placeholder="Search translated documents"
            onSearch={(value) => {
              setSearchValue(value);
              setCurrentPage(1);
            }}
            style={{ maxWidth: "400px" }}
            className="search-meetings w-100 w-sm-auto"
          />
        </div>
      </div>
      <div className="th-action-row">
        <Checkbox
          className="custom-checkbox"
          checked={actionRowChecked}
          indeterminate={actionRowIndeterminate}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedRows(history.map((item) => ({ id: item.id, file_id: item.target_file_id })));
            } else {
              setSelectedRows([]);
            }
            setActionRowChecked(e.target.checked);
            setActionRowIndeterminate(false);
          }}
        />
        <DownloadIcon onClick={handleDownloadSelected} style={{ cursor: "pointer" }} />
        <Delete onClick={handleDelete} style={{ cursor: "pointer" }} />
        <div style={{ flex: 1, display: "flex", justifyContent: "flex-end" }}>
          <RangePicker
            className="date-range"
            format="DD-MM-YYYY"
            value={dateRange}
            onChange={(dates) => {
              setDateRange(dates);
              setFilterType('all'); // Custom date range overrides menu filter
              setCurrentPage(1);
            }}
            allowClear
            placeholder={["Start date", "End date"]}
          />
        </div>
      </div>
      <div className="translate-history-table">
        {/* Table Header */}
        <div className="th-header">
          <div className="th-cell">
            {/* Checkbox removed from header */}
          </div>
          <div className="th-cell">File name</div>
          <div className="th-cell">Date</div>
          {/* <div className="th-cell">Size</div> */}
          {/* <div className="th-cell">Source language</div> */}
          <div className="th-cell">Target language</div>
          <div className="th-cell">Status</div>
          <div className="th-cell th-actions">Action items</div>
        </div>
        {loading ? (
          <div className="th-row th-loading">
            <Spin spinning />
          </div>
        ) : paginatedHistory.length === 0 ? (
          <div className="th-row th-no-data">
            <span style={{ width: '100%', textAlign: 'center', display: 'block', padding: '24px 0', color: '#888' }}>
              No data till now
            </span>
          </div>
        ) : (
          paginatedHistory.map((item) => (
            <div className="th-row" key={item.id}>
              <div className="th-cell">
                <Checkbox
                  className="custom-checkbox"
                  checked={selectedRows.some(row => row.id === item.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedRows([
                        ...selectedRows,
                        { id: item.id, file_id: item.target_file_id },
                      ]);
                    } else {
                      setSelectedRows(
                        selectedRows.filter(
                          (row) => row.id !== item.id
                        )
                      );
                    }
                  }}
                />
              </div>
              <div className="th-cell">
                <FileIcon style={{ marginRight: 8 }} />
                <span>{formatFileName(item.target_file_id)}</span>
              </div>
              <div className="th-cell">
                {moment(item.created_at).format("DD-MM-YYYY")}
              </div>
              {/* <div className="th-cell">{item.size ? item.size : "-"}</div> */}
              {/* <div className="th-cell">{item.source_language ? item.source_language: "-"}</div> */}
              <div className="th-cell">
                {getLanguageName(
                  item.target_lang,
                  documentTranslationLanguageList
                )}
              </div>
              <div className="th-cell">
                {item.status}
              </div>
              <div className="th-cell th-actions">
                <Button className="action-btn" icon={<ShareAltOutlined />} onClick={() => handleShare(item.downloadUrl)} />
                <Button
                  className="action-btn"
                  icon={<DownloadIcon />}
                  onClick={() => handleDownload(item.downloadUrl)}
                />
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
