.translateBar {
  padding-bottom: 1rem;
  &-select {
    &-not {
      display: flex;
      justify-content: space-between;
      padding: 1.5rem;
      align-items: center;
      .upload-btn {
        display: flex;
        align-items: center;
        .ant-upload {
          &-list {
            &-text-container {
              .ant-upload-list-item {
                margin: 0;
                .ant-upload-span {
                  display: flex;
                  justify-content: center;
                  gap: 0.5rem;
                  align-items: center;
                  .ant-upload-text-icon {
                    font-size: 22px;
                  }
                }
                &-name {
                  font-size: 16px;
                  color: #000;
                }
              }
            }
          }
        }
      }
    }
    &-file {
      display: flex;
      justify-content: center;
      padding: 1.5rem;
      input {
        &:-ms-input-placeholder {
          color: #000;
        }
      }
      .upload-btn {
        border-radius: 10px;
        width: 200px;
        height: 50px;
        position: relative;
        display: flex;
        align-items: center;
        .ant-upload {
          width: 100%;
          height: 100%;
          border-radius: 10px;
          .ant-btn {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            background-color: #3b60e4;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            color: #fff;
            font-size: 17px;
            display: flex;
            justify-content: center;
            align-items: center;
            .anticon-upload {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}
.audio-translation {
  &-modal {
    .ant-modal-body {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
  }
  &-box {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    label {
      font-size: 16px;
      color: #000;
    }
  }
}
.jobs {
  &-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    position: relative;
  }
  &-table {
    width: 90%;
    background-color: #f5f5f5;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background-color: #3b60e4;
    thead{
      border-radius: 10px;
      color: #fff;
      tr{
        th{
          padding: 1rem;
        }
      }
    }
    tbody{
      tr{
        &:nth-child(odd){
          background-color: #f5f5f5;
        }
        &:nth-child(even){
          background-color: #fff;
        }
      }
    }
  }
  &-item {
    background-color: #f5f5f5;
    .failed{
      div{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #FFE1E1;
        border-radius: 10px;
        padding: 1px 5px;
        color: #DC0000;
      }
    }
    .completed{
      div{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #E1FFD6;
        border-radius: 10px;
        padding: 1px 5px;
        color: #00A86B;
      }
    }
    .queued{
      div{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #E1E1FF;
        border-radius: 10px;
        padding: 1px 5px;
        color: #0000DC;
      }
    }
    .in_progress{
      div{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #FFF0E1;
        border-radius: 10px;
        padding: 1px 5px;
        color: #FFA500;
      }
    }
    .final_processing{
      div{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #E1E1E1;
        border-radius: 10px;
        padding: 1px 5px;
        color: #000000;
      }
    }
    td{
        padding: 0.5rem 1rem;
    }
  }
}
.audio-translation-loader{
  width: 80px;
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span{
    background-color: #3b60e4;
    width: 6px;
    height: 5px;
    display: block;
    border-radius: 50%;
    &:nth-child(1){
      animation: bounce 1s infinite;
    }
    &:nth-child(2){
      animation: bounce 1s infinite;
      animation-delay: 0.1s;
    }
    &:nth-child(3){
      animation: bounce 1s infinite;
      animation-delay: 0.2s;
    }
    &:nth-child(4){
      animation: bounce 1s infinite;
      animation-delay: 0.3s;
    }
    &:nth-child(5){
      animation: bounce 1s infinite;
      animation-delay: 0.4s;
    }
    &:nth-child(6){
      animation: bounce 1s infinite;
      animation-delay: 0.5s;
    }
    &:nth-child(7){
      animation: bounce 1s infinite;
      animation-delay: 0.6s;
    }
    &:nth-child(8){
      animation: bounce 1s infinite;
      animation-delay: 0.7s;
    }
  }
}
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}