import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { AccountService } from "../../Services/AccountService";

export const initialState = {
  loading: true,
  message: "",
  tableData: [],
  inactiveTableData: [],
  activelicenseData: [],
  count: null,
  inactiveCount: null,
  activelicenceCount: null,
  inactiveLicensesDataCount: null,
};

export const fetchTableData = createAsyncThunk(
  "table/fetchTableData",
  async (fetchTableDate, { rejectWithValue }) => {
    const payload = {
      starting_date: fetchTableDate?.starting_date,
      ending_date: fetchTableDate?.ending_date,
    };
    try {
      const response = await AccountService.fetchTableData(
        payload
      );
      const result = await response.json();
      if (!response.ok) {
        throw result?.message;
      }
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchActiveTableData = createAsyncThunk(
  "table/fetchData",
  async (data, { rejectWithValue }) => {
    const payload = {
      data_required: data?.data_required,
      currentPage: data?.currentPage,
      pageSize: data?.pageSize,
      starting_date: data?.starting_date,
      ending_date: data?.ending_date,
    };
    try {
      const response = await AccountService.fetchActiveTableData(
        payload
      );
      if (!response.ok) {
        throw new Error("Failed to fetch table data");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchInactiveTableData = createAsyncThunk(
  "table/fetchinactiveData",
  async (data, { rejectWithValue }) => {
    const payload = {
      data_required: data?.data_required,
      currentPage: data?.currentPage,
      pageSize: data?.pageSize,
      starting_date: data?.starting_date,
      ending_date: data?.ending_date,
    };
    try {
      const response = await AccountService.fetchInActiveTableData(
        payload
      );
      if (!response.ok) {
        throw new Error("Failed to fetch data");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchActiveLicenseData = createAsyncThunk(
  "table/fetchActiveLicenseData",
  async (data, { rejectWithValue }) => {
    const payload = {
      data_required: data?.data_required,
      currentPage: data?.currentPage,
      pageSize: data?.pageSize,
      starting_date: data?.starting_date,
      ending_date: data?.ending_date,
    };
    try {
      const response = await AccountService.fetchActiveLicenseData(
        payload
      );
      if (!response.ok) {
        throw new Error("Failed to fetch data");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const tableSlice = createSlice({
  name: "table",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchTableData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTableData.fulfilled, (state, action) => {
        state.loading = false;
        state.count = action.payload?.data?.activeAccountDataCount;
        state.inactiveCount = action.payload?.data?.inactiveAccountDataCount;
        state.activelicenceCount =
          action.payload?.data?.activeLicensesDataCount;
        state.inactiveLicensesDataCount =
          action.payload?.data?.inactiveLicensesDataCount;
        state.message = action.payload?.message;
      })
      .addCase(fetchTableData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message;
      })
      .addCase(fetchActiveTableData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchActiveTableData.fulfilled, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
        state.tableData = action.payload?.data?.allData;
        state.count = action.payload?.data?.count;
      })
      .addCase(fetchActiveTableData.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
      })
      .addCase(fetchInactiveTableData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchInactiveTableData.fulfilled, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
        state.inactiveTableData = action.payload?.data?.allData;
        state.inactiveCount = action.payload?.data?.count;
      })
      .addCase(fetchInactiveTableData.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
      })
      .addCase(fetchActiveLicenseData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchActiveLicenseData.fulfilled, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
        state.activelicenseData = action.payload?.data?.allData;
      })
      .addCase(fetchActiveLicenseData.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
      });
  },
});

export default tableSlice.reducer;
