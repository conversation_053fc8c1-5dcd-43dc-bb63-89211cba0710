import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { AUTH_TOKEN } from "../../Constants/AuthConstant";

const baseUrl = process.env.REACT_APP_STAG_BACKEND_BASE_URL;

const initialState = {
  loading: false,
  message: "",
  userDetails: {},
  request_id: "",
};

export const fetchUserData = createAsyncThunk(
  "profile/fetchUserData",
  async (fetchTableDate, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${baseUrl}/saas/masterAdmin/user/details`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
      if (!response.ok) {
        throw new Error("Failed to Fetch Data");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const sendOtp = createAsyncThunk(
  "profile/sendOtp",
  async (email, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${baseUrl}/admin/send/update/otp`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem(AUTH_TOKEN)}`,
          },
          body: JSON.stringify({
            email,
            type: "UPDATE_DETAILS",
          }),
        }
      );
      const result = await response.json();
      if (!response.ok) {
        throw result?.message;
      }
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const verifyOtp = createAsyncThunk(
  "profile/verifyOtp",
  async (data, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${baseUrl}/admin/verify/otp`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem(AUTH_TOKEN)}`,
          },
          body: JSON.stringify({
            ...data,
          }),
        }
      );
      if (!response.ok) {
        throw new Error("Failed to send the details");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const profileSlice = createSlice({
  name: "profile",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserData.fulfilled, (state, action) => {
        state.loading = false;
        state.userDetails = action.payload?.data?.userDetails;
        state.message = action.payload?.message;
      })
      .addCase(fetchUserData.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.meesage;
      })
      .addCase(sendOtp.fulfilled, (state, action) => {
        state.message = action.payload?.message;
        state.request_id = action.payload?.data?.request_id;
      })
      .addCase(sendOtp.rejected, (state, action) => {
        state.message = action.payload;
      });
  },
});

export default profileSlice.reducer;
