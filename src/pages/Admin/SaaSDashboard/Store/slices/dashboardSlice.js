import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import moment from "moment";
import { DashboardService } from "../../Services/DashboardService";

const initialState = {
  loading: true,
  message: "",
  dues: null,
  net_revenue: null,
  revenue: null,
  totalPercentage: null,
  changeInPercentage: null,
  totalMeetCount: null,
  totalMeetHours: null,
  totalRevenueValue: null,
  revenueData: [],
  previousMonthRevenue: [],
  totalRevenue: [],
  totalMeetingData: [],
  totalMeetingHoursData: [],
  totalRevenueStartingDate: "",
  totalRevenueEndingDate: "",
  totalMeetingHoursStartingDate: "",
  totalMeetingHoursEndingDate: "",
  totalDashBoardStartingDate: "",
  totalDashBoardEndingDate: "",
  consistentDateAcrossDifferentChart: {
    starting_date: "",
    ending_date: "",
  },
};

export const previousMonthRevenueData = createAsyncThunk(
  "dashboard/previousMonthRevenueData",
  async (previousMonthRevenueDate, { rejectWithValue }) => {
    try {
      const response = await DashboardService.previousMonthRevenueData(
        previousMonthRevenueDate
      );
      if (!response.ok) {
        throw new Error("Failed to get data");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const totalRevenueData = createAsyncThunk(
  "dashboard/totalRevenue",
  async (totalRevenueDate, { rejectWithValue }) => {
    try {
      const response = await DashboardService.totalRevenueData(
        totalRevenueDate
      );
      if (!response.ok) {
        throw new Error("Failed to get data");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const totalMeeting = createAsyncThunk(
  "dashboard/totalMeeting",
  async (totalMeetingHoursDate, { rejectWithValue }) => {
    try {
      const response = await DashboardService.totalMeeting(
        totalMeetingHoursDate
      );
      if (!response.ok) {
        throw new Error("Failed to reteive error");
      }
      const result = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    setTotalRevenueDate(state, action) {
      state.totalRevenueStartingDate = action.payload?.starting_date;
      state.totalRevenueEndingDate = action.payload?.ending_date;
    },
    setTotalMeetingHoursDate(state, action) {
      state.totalMeetingHoursStartingDate = action.payload?.starting_date;
      state.totalMeetingHoursEndingDate = action.payload?.ending_date;
    },
    setTotalDashboardDate(state, action) {
      state.totalDashBoardStartingDate = action.payload?.starting_date;
      state.totalDashBoardEndingDate = action.payload?.ending_date;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(previousMonthRevenueData.pending, (state) => {
        state.loading = true;
      })
      .addCase(previousMonthRevenueData.fulfilled, (state, action) => {
        state.dues = action.payload?.data?.billingSnapshots?.dues;
        state.net_revenue = action.payload?.data?.billingSnapshots?.net_revenue;
        state.revenue = action.payload?.data?.billingSnapshots?.revenue;
        state.totalPercentage =
          action.payload?.data?.billingSnapshots?.totalPercentage;
        state.changeInPercentage =
          action.payload?.data?.billingSnapshots?.changeInPercentage;
        state.previousMonthRevenue =
          action.payload?.data?.prevRevGraphData?.map((curr) => ({
            date: moment(curr?.date).format("DD MMM"),
            Revenue: curr?.revenue,
            Sales: curr?.sales,
          }));
        state.consistentDateAcrossDifferentChart.starting_date =
          action.payload?.data?.date_range?.current_start_date;
        state.consistentDateAcrossDifferentChart.ending_date =
          action.payload?.data?.date_range?.current_end_date;
        state.message = action.payload?.message;
      })
      .addCase(previousMonthRevenueData.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message;
      })
      .addCase(totalRevenueData.pending, (state) => {
        state.totalRevenueLoading = true;
      })
      .addCase(totalRevenueData.fulfilled, (state, action) => {
        state.loading = false;
        state.totalRevenue = action.payload?.data?.graphData?.map((curr) => ({
          actual_date_format: curr?.date,
          date: moment(curr?.date).format("DD MMM"),
          Revenue: curr?.revenue,
          Sales: curr?.sales,
        }));
        state.totalRevenueValue =
          action.payload?.data?.totalRevenue?.totalRevenue;
        state.message = action.payload?.message;
      })
      .addCase(totalRevenueData.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload;
      })
      .addCase(totalMeeting.pending, (state) => {
        state.loading = true;
      })
      .addCase(totalMeeting.fulfilled, (state, action) => {
        state.loading = false;
        state.totalMeetCount = action.payload?.data?.totalMeet?.totalMeetCount;
        state.totalMeetHours =
          action.payload?.data?.totalMeetHours?.totalMeetHours;
        state.totalMeetingData =
          action.payload?.data?.totalMeet?.totalMeetGraphData?.map((curr) => ({
            actual_date_format: curr?.date,
            date: moment(curr?.date).format("DD MMM"),
            meetings: curr?.count,
          }));
        state.totalMeetingHoursData =
          action.payload?.data?.totalMeetHours?.totalMeetHoursGraphData?.map(
            (curr) => ({
              actual_date_format: curr?.date,
              date: moment(curr?.date).format("DD MMM"),
              meetings: curr?.total_duration,
            })
          );
      })
      .addCase(totalMeeting.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload;
      });
  },
});

export const {
  setTotalRevenueDate,
  setTotalMeetingHoursDate,
  setTotalDashboardDate,
} = dashboardSlice.actions;
export default dashboardSlice.reducer;
