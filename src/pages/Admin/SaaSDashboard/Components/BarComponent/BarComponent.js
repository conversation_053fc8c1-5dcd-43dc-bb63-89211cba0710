import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, CartesianGrid, XA<PERSON><PERSON>, Y<PERSON><PERSON><PERSON>, Tooltip, ResponsiveContainer } from 'recharts';
import { ArrowUpOutlined } from '@ant-design/icons';
import styles from './BarContainer.module.scss';

export default function BarContainer ({
  title,
  value,
  percentage,
  data,
  dataKey,
  valueKey,
  width = '100%',
  height = 250,
  color = 'rgba(0, 194, 255, 1)',
  interval
}) {
  return (
    <div className={styles.container} style={{ width }}>
      <div>
        <h3 className={styles.header}>{title}</h3>
        <div className={styles.valueContainer}>
          <h1 className={styles.value}>{value}</h1>
          <div className={styles.percentageWrapper}>
            <div className={styles.percentageContainer}>
              <p className={styles.percentageText}>{percentage}%&nbsp;</p>
              <ArrowUpOutlined rotate={45} className={styles.arrowIcon} />
            </div>
          </div>
        </div>
      </div>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} margin={{ top: 20 }}>
          <CartesianGrid stroke="#444" horizontal={false} vertical={false} />
          <XAxis
            dataKey={dataKey}
            stroke="#ccc"
            axisLine={false}
            tickLine={false}
            dy={10}
            interval={interval}
          />
          <YAxis stroke="#ccc" axisLine={false} tickLine={false} dx={-10} />
          <Tooltip />
          <Bar dataKey={valueKey} fill={color} barSize={10} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};
