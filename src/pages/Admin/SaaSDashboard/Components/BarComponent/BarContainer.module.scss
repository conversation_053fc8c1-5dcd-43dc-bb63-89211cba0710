.container {
  width: 100%;
  padding: 20px;
}

.header {
  color: white;
  margin: 0;
  font-size: 2rem;
  @media screen and (min-width: 1000px) {
    div{
      font-size: 22px;
    }
  }
}

.valueContainer {
  display: flex;
  align-items: center;
}

.value {
  margin: 0;
  color: white;
  font-size: 2rem;
  @media screen and (min-width: 1000px) {
    div{
      font-size: 32px;
    }
  }
}

.percentageWrapper {
  margin-left: 0.5rem;
}

.percentageContainer {
  display: flex;
  border: 1px solid rgba(5, 193, 104, 0.2);
  border-radius: 8px;
  background-color: rgba(5, 193, 104, 0.2);
  align-items: center;
  padding: 8px;
  height: 1.6rem;
}

.percentageText {
  margin: 0;
  font-size: 0.8rem;
  color: rgba(20, 202, 116, 1);
}

.arrowIcon {
  font-size: 0.8rem;
  color: rgba(20, 202, 116, 1);
}
