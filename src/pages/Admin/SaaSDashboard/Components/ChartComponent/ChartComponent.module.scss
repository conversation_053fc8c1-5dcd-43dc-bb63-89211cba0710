.chart {
  border-radius: 10px;
  &Header {
    display: flex;
    flex-direction: column;
    gap: 5px;
    &Top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      h3{
        font-size: 2rem;
        color: white;
        margin: 0;
      }
    }
    &Bottom{
      display: flex;
      align-items: center;
      gap: 20px;
    }
    &More{
      font-size: 1.5rem;
      color: white;
      cursor: pointer;
    }
  }
  &PercentageChange{
    display: flex;
    align-items: center;
    gap: 5px;
    p{
      margin: 0;
    }
    &Up{
      color: rgba(20, 202, 116, 1);
      background-color: rgba(20, 202, 116, 0.2);
      border: 1px solid rgba(20, 202, 116, 0.2);
      border-radius: 8px;
      padding: 0 10px;
      &Icon{
        color: rgba(20, 202, 116, 1);
        rotate: 45deg;
      }
    }
    &Down{
      color: red;
      background-color: rgba(255, 0, 0, 0.2);
      border: 1px solid rgba(255, 0, 0, 0.2);
      border-radius: 8px;
      padding: 3px 10px;
      &Icon{
        color: red;
        rotate: -45deg;
      }
    }
  }
}


.title {
  margin: 0;
  font-size: 1.2rem;
  color: white;
}

.subtitle {
  margin: 0;
  font-size: 2rem;
  color: white;
}

.icon {
  font-size: 1.5rem;
  color: white;
}

.percentageChange {
  display: flex;
  height: 1.6rem;
  aspect-ratio: 2.5;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px solid rgba(5, 193, 104, 0.2);
  background-color: rgba(5, 193, 104, 0.2);
  color: rgba(20, 202, 116, 1);
}