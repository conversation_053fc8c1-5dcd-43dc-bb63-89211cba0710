import React from "react";
import PropTypes from "prop-types";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON>Axis,
  Tooltip,
  Area,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import { DatePicker } from "antd";
import moment from "moment";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { DARK_BLUE_SHADE } from "../../Constants/ThemeConstant";
import styles from "./ChartComponent.module.scss";
import { DATE_FORMAT_DD_MMM_SLASH } from "../../Constants/DateConstant";
import { setTotalRevenueDate } from "../../Store/slices/dashboardSlice";

dayjs.extend(customParseFormat);

function ChartComponent({
  title,
  subtitle,
  chartData,
  xAxisDataKey,
  yAxisFormatter,
  areaDataKeys,
  colors,
  legendPosition = "top",
  height = 400,
  percentageChange,
  horizontal,
  vertical,
  tickCount,
  dateppicker,
  more,
  interval,
  setDate,
  dispatch,
  startDate,
  endDate,
}) {
  // console.log(date);
  const handleDateChange = (dates) => {
    setDate({
      starting_date: dates ? moment(dates[0]?.$d).format("YYYY-MM-DD") : "",
      ending_date: dates ? moment(dates[1]?.$d).format("YYYY-MM-DD") : "",
    });
    dispatch(
      setTotalRevenueDate({
        starting_date: dates ? moment(dates[0]?.$d).format("YYYY-MM-DD") : "",
        ending_date: dates ? moment(dates[1]?.$d).format("YYYY-MM-DD") : "",
      })
    );
  };

  const disableFutureDates = (current) => {
    return current && current.isAfter(moment(), "day"); // Disable future dates
  };

  return (
    <div className={styles.chart}>
      {/* Header Section */}
      <div className={styles.chartHeader}>
        <div className={styles.chartHeaderTop}>
          <h3>{title}</h3>
          {dateppicker && (
            <DatePicker.RangePicker
              placeholder={[
                moment(startDate).format(DATE_FORMAT_DD_MMM_SLASH),
                moment(endDate).format(DATE_FORMAT_DD_MMM_SLASH),
              ]}
              style={{
                backgroundColor: `${DARK_BLUE_SHADE}`,
                color: "white",
                width: "13rem",
                borderWidth: "0px",
              }}
              format={DATE_FORMAT_DD_MMM_SLASH}
              onChange={handleDateChange}
              disabledDate={disableFutureDates}
            />
          )}
          {more && <MoreOutlined className={styles.chartHeaderMore} />}
        </div>
        <div className={styles.chartHeaderBottom}>
          <h1 className={styles.subtitle}>{subtitle}</h1>
          {/* Dynamic Percentage Change Display */}
          {percentageChange && (
            <div className={styles.chartPercentageChange}>
              <p 
                className={
                  percentageChange > 0 ? styles.chartPercentageChangeUp : styles.chartPercentageChangeDown
                }
              >
                {percentageChange}%
              </p>
              {percentageChange > 0 ? (
                <ArrowUpOutlined className={styles.chartPercentageChangeUpIcon} />
              ) : (
                <ArrowDownOutlined className={styles.chartPercentageChangeDownIcon}/>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Chart Section */}
      <ResponsiveContainer width="100%" height={height}>
        {chartData.length !== 0 ? (
          <AreaChart
            data={chartData}
            margin={{ top: 10, right: 30, left: 0, bottom: 20 }}
          >
            <defs>
              {areaDataKeys.map((key, index) => (
                <linearGradient
                  key={key}
                  id={`color${key}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop
                    offset="5%"
                    stopColor={colors[index].start}
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor={colors[index].end}
                    stopOpacity={0}
                  />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid
              stroke="white"
              horizontal={horizontal}
              vertical={vertical}
            />
            <XAxis
              dataKey={xAxisDataKey}
              stroke="#ccc"
              dy={20}
              axisLine={false}
              tickLine={false}
              interval={interval}
            />
            <YAxis
              tickCount={tickCount}
              tickFormatter={yAxisFormatter}
              stroke="#ccc"
              dx={-10}
              axisLine={false}
              tickLine={false} // Format tick values for decimals
              minTickGap={10}
            />

            <Tooltip />
            <Legend
              verticalAlign={legendPosition}
              wrapperStyle={{ paddingBottom: "20px" }}
            />
            {areaDataKeys.map((key, index) => (
              <Area
                key={key}
                type="monotone"
                dataKey={key}
                stroke={colors[index].stroke}
                fillOpacity={1}
                fill={`url(#color${key})`}
              />
            ))}
          </AreaChart>
        ) : (
          <div
            style={{
              height,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            No Data to Display
          </div>
        )}
      </ResponsiveContainer>
    </div>
  );
}

ChartComponent.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string,
  chartData: PropTypes.arrayOf(
    PropTypes.objectOf(PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.bool,
    ]))
  ).isRequired,
  xAxisDataKey: PropTypes.string.isRequired,
  yAxisFormatter: PropTypes.func,
  areaDataKeys: PropTypes.arrayOf(PropTypes.string).isRequired,
  colors: PropTypes.arrayOf(
    PropTypes.shape({
      start: PropTypes.string.isRequired,
      end: PropTypes.string.isRequired,
      stroke: PropTypes.string.isRequired,
    })
  ).isRequired,
  legendPosition: PropTypes.string,
  height: PropTypes.number,
  // iconType: PropTypes.oneOf(["up", "down"]),
  percentageChange: PropTypes.number,
  horizontal: PropTypes.bool,
  vertical: PropTypes.bool,
  tickCount: PropTypes.number,
  dateppicker: PropTypes.bool,
  more: PropTypes.bool,
  interval: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  setDate: PropTypes.func,
  dispatch: PropTypes.func,
  startDate: PropTypes.string,
  endDate: PropTypes.string,
};

// Default props to avoid ESLint warnings
ChartComponent.defaultProps = {
  title: '',
  subtitle: '',
  yAxisFormatter: (value) => value,
  legendPosition: 'top',
  height: 400,
  // iconType: undefined,
  percentageChange: undefined,
  horizontal: true,
  vertical: false,
  tickCount: 5,
  dateppicker: false,
  more: false,
  interval: 0,
  setDate: () => {},
  dispatch: () => {},
  startDate: '',
  endDate: '',
};

export default ChartComponent;
