import React from "react";
import { Statistic } from "antd";
import { ArrowUpOutlined } from "@ant-design/icons";
import styles from "./StatisticCard.module.scss";

function StatisticCard({
  title = "Statistic Title",
  value = 0,
  suffixValue = "",
  percentage = 0,
  isPositive = true,
  onClick,
}) {
  return (
    <div
      className={`${styles.cardContainer} ${onClick ? styles.clickable : ""}`}
      onClick={onClick}
    >
      <Statistic
        title={title}
        value={value}
        className={styles.statisticCard}
        suffix={
          <div className={styles.suffixContainer}>
            <p className={styles.suffixValue}>{suffixValue}</p>
            <div
              className={styles.percentageBox}
              style={{
                borderColor: isPositive
                  ? "rgba(5, 193, 104, 0.2)"
                  : "rgba(220, 20, 60, 0.2)",
                backgroundColor: isPositive
                  ? "rgba(5, 193, 104, 0.2)"
                  : "rgba(220, 20, 60, 0.2)",
              }}
            >
              <i
                className={styles.percentageText}
                style={{
                  color: isPositive
                    ? "rgba(20, 202, 116, 1)"
                    : "rgba(220, 20, 60, 1)",
                }}
              >
                {percentage}%&nbsp;
              </i>
              <ArrowUpOutlined
                rotate={isPositive ? 45 : 135}
                style={{
                  fontSize: "0.8rem",
                  color: isPositive
                    ? "rgba(20, 202, 116, 1)"
                    : "rgba(220, 20, 60, 1)",
                }}
              />
            </div>
          </div>
        }
        valueStyle={{ color: "white" }}
      />
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        {/* <MoreOutlined rotate={90} className={styles.moreIcon} /> */}
        {/* {viewall && (
          <div onClick={onClick} className={styles.viewStyles} style={{border: "1px solid red"}}>
            <p style={{ margin: "0px", color: "rgba(174, 185, 225, 1)", fontSize: "0.8em" , border: "1px solid red"}}>View All</p>
            <AiOutlineArrowRight />
          </div>
        )} */}
      </div>
    </div>
  );
}

export default StatisticCard; 