$primary-color: #0a1330;
$secondary-color: #5c59e8;
$primary-text-color: #aeb9e1;

.cardContainer {
  background-color: $primary-color;
  box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
  padding: 20px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
  &.clickable:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
  }
  &:not(.clickable):hover {
    cursor: default;
  }
}

.suffixContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 1rem;
}

.suffixValue {
  margin-bottom: 0;
  color: white;
}

.percentageBox {
  display: flex;
  border: 1px solid white;
  border-radius: 8px;
  align-items: center;
  padding: 8px;
  height: 1.6rem;
}

.percentageText {
  margin: 0;
  font-size: 0.8rem;
}

.moreIcon {
  font-size: 1.5rem;
  color: white;
  align-self: flex-end;
}

.viewStyles {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(174, 185, 225, 1);
}

.viewStyles:hover {
  cursor: pointer;
  text-decoration: underline;
}

:global(.ant-statistic-content) {
  display: flex;
  align-items: center;
}
