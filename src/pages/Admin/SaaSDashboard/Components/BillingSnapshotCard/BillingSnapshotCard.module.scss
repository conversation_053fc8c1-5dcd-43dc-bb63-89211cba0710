.snapshot {
  height: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: space-between;
  &<PERSON>er {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &Title {
    color: white;
    margin: 0;
  }
  &Progress {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 10px;
    &Circle{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
      p{
        color: white;
        font-size: 16px;
        font-weight: 400;
        text-wrap: nowrap;
      }
    }
  }
  &Stats{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
  }
  &Statistic{
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    :global(.ant-statistic-content-value){
      font-size: 16px;
      color: white;
      font-weight: 400;
      text-wrap: nowrap;
    }
    &Title{
      font-size: 16px;
      color: white;
      font-weight: 400;
      text-wrap: nowrap;
    }
    &Suffix{
      display: flex;
      align-items: center;
      gap: 5px;
    }
    &Value{
      font-size: 16px;
      color: white;
      font-weight: 400;
      text-wrap: nowrap;
    }
  }
}

.title {
  color: white;
}

.icon {
  font-size: 1.5rem;
  color: white;
}

.progressWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progressContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progressPercent {
  color: white;
  margin-bottom: 0;
}

.revenueGrowth {
  background-color: rgba(231, 244, 238, 1);
  width: 3rem;
  height: 1.5rem;
  padding: 0 2.5rem;
  border-radius: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  &Negative{
    background-color: rgba(255, 230, 230, 1);
    .revenueText{
      color: rgba(255, 99, 71, 1);
    }
  }
}

.revenueText {
  color: rgba(13, 137, 79, 1);
  margin: 0;
  font-size: 0.9rem;
  font-weight: bold;
}

.footerText {
  color: #ccc;
  margin-top: 20px;
}

.statsRow {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.statistic {
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.suffix {
  display: flex;
}

.arrowIcon {
  font-size: 1.2rem;
}
