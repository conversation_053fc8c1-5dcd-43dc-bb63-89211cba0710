import React from "react";
import { Progress, Statistic } from "antd";
import {
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import PropTypes from "prop-types";
import styles from "./BillingSnapshotCard.module.scss"; // Importing the CSS module

function BillingSnapshotCard({
  title,
  progressPercent,
  revenueGrowth,
  footerText,
  statistics,
  onMoreClick,
}) {
  const isRevenueGrowthNegative = Number(revenueGrowth) < 0;
  return (
    <div className={styles.snapshot}>
      {/* Header Section */}
      <div className={styles.snapshotHeader}>
        <h3 className={styles.snapshotTitle}>{title}</h3>
        <MoreOutlined className={styles.icon} onClick={onMoreClick} />
      </div>

      {/* Progress Circle Section */}
      <div className={styles.snapshotProgress}>
        <div className={styles.snapshotProgressCircle}>
          <Progress
            type="circle"
            percent={progressPercent}
            format={(percent) => (
              <div className={styles.progressContent}>
                <p className={styles.progressPercent}>{percent}%</p>
                <div
                  className={
                    isRevenueGrowthNegative
                      ? `${styles.revenueGrowth} ${styles.revenueGrowthNegative}`
                      : styles.revenueGrowth
                  }
                >
                  <p className={styles.revenueText}>{revenueGrowth}%</p>
                </div>
              </div>
            )}
            size={180}
          />
          <p>{footerText}</p>
        </div>

        {/* Dynamic Statistics Row */}
        <div className={styles.snapshotStats}>
          {statistics.map((stat) => (
              <Statistic
                className={styles.snapshotStatistic}
                title={<p className={styles.snapshotStatisticTitle}>{stat.title}</p>}
                value={`\u20B9 ${stat.value}`}
                suffix={
                  <div className={styles.snapshotStatisticSuffix} onClick={stat.onClick}>
                    {stat.isGrowthPositive ? (
                      <ArrowUpOutlined
                        className={styles.arrowIcon}
                        style={{ color: stat.color }}
                      />
                    ) : (
                      <ArrowDownOutlined
                        className={styles.arrowIcon}
                        style={{ color: stat.color }}
                      />
                    )}
                  </div>
                }
              />
          ))}
        </div>
      </div>
    </div>
  );
}

// PropTypes for type checking
BillingSnapshotCard.propTypes = {
  title: PropTypes.string,
  progressPercent: PropTypes.number,
  revenueGrowth: PropTypes.number,
  footerText: PropTypes.string,
  statistics: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
      onClick: PropTypes.func,
      isGrowthPositive: PropTypes.bool,
      color: PropTypes.string,
    })
  ),
  onMoreClick: PropTypes.func,
};

// Default props to avoid ESLint warnings
BillingSnapshotCard.defaultProps = {
  title: "",
  progressPercent: 0,
  revenueGrowth: 0,
  footerText: "",
  statistics: [],
  onMoreClick: () => {},
};

export default BillingSnapshotCard;
