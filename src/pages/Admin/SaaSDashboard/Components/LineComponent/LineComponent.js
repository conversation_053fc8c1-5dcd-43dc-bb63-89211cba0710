import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Line,
} from "recharts";
import { MoreOutlined, ArrowUpOutlined } from "@ant-design/icons";
import { DatePicker } from "antd";
import moment from "moment";
import styles from "./LineContainer.module.scss";
import { DARK_BLUE_SHADE } from "../../Constants/ThemeConstant";
import { DATE_FORMAT_DD_MMM_SLASH } from "../../Constants/DateConstant";
import { setTotalMeetingHoursDate } from "../../Store/slices/dashboardSlice";

function LineContainer({
  title,
  value,
  valueChange,
  valueChangeColor,
  data,
  dataKeyPrimary,
  colorPrimary,
  height = 390,
  datepicker,
  more,
  interval,
  setDate,
  dispatch,
  startDate,
  endDate
}) {
  const handleDateChange = (dates) => {
    setDate({
      starting_date: dates ? moment(dates[0]?.$d).format("YYYY-MM-DD") : "",
      ending_date: dates ? moment(dates[1]?.$d).format("YYYY-MM-DD") : "",
    });
    dispatch(setTotalMeetingHoursDate({
      starting_date: dates ? moment(dates[0]?.$d).format("YYYY-MM-DD") : "",
      ending_date: dates ? moment(dates[1]?.$d).format("YYYY-MM-DD") : "",
    }))
  };

  const disableFutureDates = (current) => {
    return current && current.isAfter(moment(), "day"); // Disable future dates
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div style={{ width: "100%" }}>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <h3 className={styles.title}>{title}</h3>
            <div>
              {datepicker && (
                <DatePicker.RangePicker
                  placeholder={[
                    moment(startDate).format(
                      DATE_FORMAT_DD_MMM_SLASH
                    ),
                    moment(endDate).format(
                      DATE_FORMAT_DD_MMM_SLASH
                    ),
                  ]}
                  style={{
                    backgroundColor: `${DARK_BLUE_SHADE}`,
                    color: "white",
                    width: "13rem",
                    borderWidth: "0px",
                  }}
                  format={DATE_FORMAT_DD_MMM_SLASH}
                  onChange={handleDateChange}
                  disabledDate={disableFutureDates}
                />
              )}
              {more && <MoreOutlined className={styles.moreIcon} />}
            </div>
          </div>
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <h1 className={styles.value}>{value}</h1>
            {valueChange && (
              <div className={styles.changeContainer}>
                <p style={{ color: valueChangeColor, margin: "0px" }}>
                  {valueChange}%
                </p>
                <ArrowUpOutlined
                  style={{ color: valueChangeColor, fontSize: "0.8rem" }}
                  rotate={45}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      <ResponsiveContainer height={height}>
        <LineChart
          data={data}
          margin={{ top: 20, bottom: 10, right: 40, left: 0 }}
        >
          <CartesianGrid horizontal={false} vertical={false} stroke="#444" />
          <XAxis
            dataKey="date"
            stroke="#ccc"
            axisLine={false}
            tickLine={false}
            dy={20}
            interval={interval}
          />
          <YAxis stroke="#ccc" axisLine={false} tickLine={false} dx={-20} />
          <Tooltip />
          <Line
            type="linear"
            dataKey={dataKeyPrimary}
            stroke={colorPrimary}
            dot={{ r: 0 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

export default LineContainer;
