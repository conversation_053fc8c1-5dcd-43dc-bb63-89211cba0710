.container {
  padding: 20px;
  border-radius: 8px;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.title {
  margin: 0;
  color: white;
  font-size: 2rem;
  @media screen and (min-width: 1000px) {
    div{
      font-size: 22px;
    }
  }
}

.value {
  margin: 0;
  color: white;
  font-size: 2rem;
  @media screen and (min-width: 1000px) {
  div{
      font-size: 32px;
    }
  }
}

.changeContainer {
  display: flex;
  align-items: center;
  height: 1.6rem;
  aspect-ratio: 2.5;
  justify-content: center;
  background-color: rgba(5, 193, 104, 0.2);
  border: 1px solid rgba(5, 193, 104, 0.2);
  border-radius: 8px;
}

.moreIcon {
  font-size: 1.5rem;
  color: white;
}
