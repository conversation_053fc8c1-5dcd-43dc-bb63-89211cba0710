import React, { useEffect, useState } from "react";
import { ArrowUpOutlined } from "@ant-design/icons";
import moment from "moment";
import { <PERSON><PERSON>, DatePicker, Badge } from "antd";
import {
  // connect,
  useDispatch,
  useSelector,
} from "react-redux";
import { useNavigate } from "react-router-dom";
import { FaRegUser } from "react-icons/fa";
import { DATE_FORMAT_DD_MMM_YY } from "./Constants/DateConstant";
import styles from "./Styles/defaultDashBoard.module.scss";
import StatisticCard from "./Components/StatisticCard/StatisticCard";
import BillingSnapshotCard from "./Components/BillingSnapshotCard/BillingSnapShotCard";
import ChartComponent from "./Components/ChartComponent/ChartComponent";
import LineContainer from "./Components/LineComponent/LineComponent";
import BarContainer from "./Components/BarComponent/BarComponent";
import { fetchTableData } from "./Store/slices/accountSlice";
import {
  previousMonthRevenueData,
  setTotalDashboardDate,
  totalMeeting,
  totalRevenueData,
} from "./Store/slices/dashboardSlice";
import { fetchUserData } from "./Store/slices/profileSlice";
import adminRoutesMap from "../../../routeControl/adminRoutes";

// Import the new page components - REMOVED since we're using routing now
// import { ActiveAccounts, InactiveAccounts, ActiveLicense } from "./Pages";

export default function SaaSDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Remove page navigation state since we're using React Router now
  // const [currentPage, setCurrentPage] = useState('dashboard');
  // const [pageProps, setPageProps] = useState(null);
  const [totalRevenueDate, setTotalRevenueDate] = useState({
    starting_date: "",
    ending_date: "",
  });
  const [totalMeetingHoursDate, settotalMeetingHoursDate] = useState({
    starting_date: "",
    ending_date: "",
  });
  const [fetchTableDate, setFetchTableDate] = useState({
    starting_date: "",
    ending_date: "",
  });
  const [revenueinterval, setRevenueInterval] = useState(3);
  const [revenuehoursinterval, setRevenueHoursInterval] = useState(3);
  const {
    totalRevenueStartingDate,
    totalRevenueEndingDate,
    totalMeetingHoursStartingDate,
    totalMeetingHoursEndingDate,
    totalDashBoardStartingDate,
    totalDashBoardEndingDate,
    dues,
    net_revenue: netRevenue,
    revenue,
    totalPercentage,
    previousMonthRevenue,
    changeInPercentage,
    totalRevenue,
    totalMeetingData,
    totalMeetingHoursData,
    totalMeetCount,
    totalMeetHours,
    totalRevenueValue,
    consistentDateAcrossDifferentChart,
  } = useSelector((state) => state.dashboard || {});

  const { count, inactiveCount, activelicenceCount } = useSelector(
    (state) => state.table || {}
  );
  const { userDetails } = useSelector((state) => state.saaProfile || {});
  const totalPreviousMonthRevenue = previousMonthRevenue.reduce(
    (acc, curr) => acc + curr.Revenue,
    0
  );

  const colorsOne = [
    {
      start: "rgba(125, 122, 237, 0.5)",
      end: "rgba(233, 136, 65, 0)",
      stroke: "rgba(92, 89, 232, 1)",
    },
    {
      start: "rgba(233, 136, 65, 0.5)",
      end: "rgba(233, 136, 65, 0)",
      stroke: "rgba(228, 106, 17, 1)",
    },
  ];
  const colorsTwo = [
    {
      start: "rgba(87, 93, 255, 0.2)",
      end: "rgba(87, 93, 255, 0)",
      stroke: "rgba(203, 60, 255, 1)",
    },
    {
      start: "rgba(87, 93, 255, 0.2)",
      end: "rgba(87, 93, 255, 0)",
      stroke: "rgba(87, 93, 255, 1)",
    },
  ];

  useEffect(() => {
    const globallyStoringTotalDashboardDate = {
      starting_date: totalDashBoardStartingDate,
      ending_date: totalDashBoardEndingDate,
    };
    if (
      globallyStoringTotalDashboardDate?.starting_date &&
      globallyStoringTotalDashboardDate?.ending_date
    ) {
      dispatch(fetchTableData(globallyStoringTotalDashboardDate));
      dispatch(previousMonthRevenueData(globallyStoringTotalDashboardDate));
      dispatch(fetchUserData(globallyStoringTotalDashboardDate));
    } else {
      dispatch(fetchTableData(fetchTableDate));
      dispatch(previousMonthRevenueData(globallyStoringTotalDashboardDate));
      dispatch(fetchUserData(globallyStoringTotalDashboardDate));
    }
  }, [
    dispatch,
    fetchTableDate,
    totalDashBoardStartingDate,
    totalDashBoardEndingDate,
  ]);

  useEffect(() => {
    if (
      (totalRevenueDate?.starting_date && totalRevenueDate?.ending_date) ||
      (totalRevenueStartingDate && totalRevenueEndingDate) ||
      (consistentDateAcrossDifferentChart.starting_date &&
        consistentDateAcrossDifferentChart?.ending_date)
    ) {
      const startDate = moment(
        totalRevenueDate?.starting_date ||
          totalRevenueStartingDate ||
          consistentDateAcrossDifferentChart.starting_date
      );
      const endDate = moment(
        totalRevenueDate?.ending_date ||
          totalRevenueEndingDate ||
          consistentDateAcrossDifferentChart?.ending_date
      );

      const rangeInDays = endDate.diff(startDate, "days");
      const interval = rangeInDays > 15 ? 5 : 3;
      setRevenueInterval(interval);
    }
    const globallyStoringRevenueDate = {
      starting_date: totalRevenueStartingDate,
      ending_date: totalRevenueEndingDate,
    };
    if (
      globallyStoringRevenueDate?.starting_date &&
      globallyStoringRevenueDate?.ending_date
    ) {
      dispatch(totalRevenueData(globallyStoringRevenueDate));
    } else {
      dispatch(totalRevenueData(totalRevenueDate));
    }
  }, [
    dispatch,
    totalRevenueDate,
    consistentDateAcrossDifferentChart,
    totalRevenueStartingDate,
    totalRevenueEndingDate,
  ]);

  useEffect(() => {
    const globallyStoringTotalMeetingHoursDate = {
      starting_date: totalMeetingHoursStartingDate,
      ending_date: totalMeetingHoursEndingDate,
    };
    if (
      (totalMeetingHoursDate?.starting_date &&
        totalMeetingHoursDate?.ending_date) ||
      (globallyStoringTotalMeetingHoursDate?.starting_date &&
        totalMeetingHoursDate?.ending_date) ||
      (consistentDateAcrossDifferentChart.starting_date &&
        consistentDateAcrossDifferentChart?.ending_date)
    ) {
      const startDate = moment(
        totalRevenueDate?.starting_date ||
          globallyStoringTotalMeetingHoursDate?.starting_date ||
          consistentDateAcrossDifferentChart.starting_date
      );
      const endDate = moment(
        totalRevenueDate?.ending_date ||
          globallyStoringTotalMeetingHoursDate?.ending_date ||
          consistentDateAcrossDifferentChart.ending_date
      );
      const rangeInDays = endDate.diff(startDate, "days");

      const interval = rangeInDays > 15 ? 5 : 3;
      setRevenueHoursInterval(interval);
    }
    if (
      globallyStoringTotalMeetingHoursDate?.starting_date &&
      globallyStoringTotalMeetingHoursDate?.ending_date
    ) {
      dispatch(totalMeeting(globallyStoringTotalMeetingHoursDate));
    } else {
      dispatch(totalMeeting(totalMeetingHoursDate));
    }
  }, [
    dispatch,
    totalMeetingHoursDate,
    consistentDateAcrossDifferentChart,
    totalMeetingHoursStartingDate,
    totalMeetingHoursEndingDate,
  ]);

  const statisticsData = [
    {
      title: "Total Dues",
      value: !dues ? 0 : dues.toFixed(2),
      isGrowthPositive: false,
      color: "rgba(243, 105, 96, 1)",
      onClick: () => console.log("Dues clicked"),
    },
    {
      title: "Total Revenue",
      value: !revenue ? 0 : revenue.toFixed(2),
      isGrowthPositive: true,
      color: "rgba(61, 161, 114, 1)",
      onClick: () => console.log("Revenue clicked"),
    },
    {
      title: "Net Revenue",
      value: !netRevenue ? 0 : netRevenue.toFixed(2),
      isGrowthPositive: true,
      color: "rgba(61, 161, 114, 1)",
      onClick: () => console.log("Net Revenue clicked"),
    },
  ];

  const handleDateChange = (dates) => {
    setFetchTableDate({
      starting_date: dates ? moment(dates[0]?.$d).format("YYYY-MM-DD") : "",
      ending_date: dates ? moment(dates[1]?.$d).format("YYYY-MM-DD") : "",
    });
    dispatch(
      setTotalDashboardDate({
        starting_date: dates ? moment(dates[0]?.$d).format("YYYY-MM-DD") : "",
        ending_date: dates ? moment(dates[1]?.$d).format("YYYY-MM-DD") : "",
      })
    );
  };

  const disableFutureDates = (current) => {
    return current && current.isAfter(moment(), "day"); // Disable future dates
  };

  const handleNavigateToPage = (page, props = null) => {
    // Use React Router navigation with query parameters for date range
    const queryParams = new URLSearchParams();
    if (props?.starting_date) queryParams.set("start", props.starting_date);
    if (props?.ending_date) queryParams.set("end", props.ending_date);

    const queryString = queryParams.toString();
    const urlWithQuery = queryString ? `?${queryString}` : "";

    switch (page) {
      case "activeAccounts":
        navigate(`${adminRoutesMap.SAAS_ACTIVE_ACCOUNTS.path}${urlWithQuery}`);
        break;
      case "inactiveAccounts":
        navigate(
          `${adminRoutesMap.SAAS_INACTIVE_ACCOUNTS.path}${urlWithQuery}`
        );
        break;
      case "activeLicense":
        navigate(`${adminRoutesMap.SAAS_ACTIVE_LICENSE.path}${urlWithQuery}`);
        break;
      default:
        console.warn(`Unknown page: ${page}`);
    }
  };

  // const handleBackToDashboard = () => {
  //   navigate(adminRoutesMap.SAAS_DASHBOARD.path);
  // };

  // Remove conditional rendering since we're using React Router now
  // if (currentPage === 'activeAccounts') {
  //   return <ActiveAccounts onBack={handleBackToDashboard} initialDateRange={pageProps} />;
  // }

  // if (currentPage === 'inactiveAccounts') {
  //   return <InactiveAccounts onBack={handleBackToDashboard} initialDateRange={pageProps} />;
  // }

  // if (currentPage === 'activeLicense') {
  //   return <ActiveLicense onBack={handleBackToDashboard} initialDateRange={pageProps} />;
  // }

  return (
    <div className={styles.dashboard}>
      <div className={styles.dashboardHeader}>
        <div>
          <h1 className={styles.dashboardTitle}>
            Welcome, {userDetails?.full_name}
          </h1>
          <p className={styles.dashboardSubtitle}>
            &quot;Measure platform consumption and track business metrics&quot;
          </p>
        </div>
        <div className={styles.dashboardHeaderButtons}>
          <Button className={styles.dashboardHeaderButtonsExport}>
            <i>Export data</i> <ArrowUpOutlined />
          </Button>
          <Button className={styles.dashboardHeaderButtonsCreateReport}>
            Create report
          </Button>
        </div>
      </div>
      <div className={styles.dashboardDatePicker}>
        <div className={styles.dashboardDatePickerDate}>
          <p>{moment(Date.now()).format(DATE_FORMAT_DD_MMM_YY)}</p>
        </div>
        <div>
          {totalDashBoardStartingDate && totalDashBoardEndingDate ? (
            <DatePicker.RangePicker
              placeholder={[
                totalDashBoardStartingDate,
                totalDashBoardEndingDate,
              ]}
              className={styles.dashboardDatePickerInput}
              onChange={handleDateChange}
              disabledDate={disableFutureDates}
            />
          ) : (
            <DatePicker.RangePicker
              className={styles.dashboardDatePickerInput}
              placeholder={[
                consistentDateAcrossDifferentChart?.starting_date,
                consistentDateAcrossDifferentChart?.ending_date,
              ]}
              onChange={handleDateChange}
              disabledDate={disableFutureDates}
            />
          )}
        </div>
      </div>
      <div className={styles.dashboardStatistics}>
        <div className={styles.dashboardStatisticsCards}>
          <div>
            <StatisticCard
              title={
                <div className={styles.dashboardStatisticsTitle}>
                  <FaRegUser />
                  <p>Total Active Customer</p>
                </div>
              }
              value={!count ? 0 : count}
              percentage={" -- "}
              isPositive
              onClick={() =>
                handleNavigateToPage("activeAccounts", {
                  starting_date:
                    totalDashBoardStartingDate ||
                    consistentDateAcrossDifferentChart?.starting_date,
                  ending_date:
                    totalDashBoardEndingDate ||
                    consistentDateAcrossDifferentChart?.ending_date,
                })
              }
            />
          </div>
          <div>
            <StatisticCard
              title={
                <div className={styles.dashboardStatisticsTitle}>
                  <FaRegUser />
                  <p>Total Inactive Customer</p>
                </div>
              }
              value={!inactiveCount ? 0 : inactiveCount}
              percentage={" -- "}
              isPositive={false}
              onClick={() =>
                handleNavigateToPage("inactiveAccounts", {
                  starting_date:
                    totalDashBoardStartingDate ||
                    consistentDateAcrossDifferentChart?.starting_date,
                  ending_date:
                    totalDashBoardEndingDate ||
                    consistentDateAcrossDifferentChart?.ending_date,
                })
              }
            />
          </div>
          <div>
            <StatisticCard
              title={
                <div className={styles.dashboardStatisticsTitle}>
                  <FaRegUser />
                  <p>Total Active Licence</p>
                </div>
              }
              value={!activelicenceCount ? 0 : activelicenceCount}
              percentage={" -- "}
              isPositive
              onClick={() =>
                handleNavigateToPage("activeLicense", {
                  starting_date:
                    totalDashBoardStartingDate ||
                    consistentDateAcrossDifferentChart?.starting_date,
                  ending_date:
                    totalDashBoardEndingDate ||
                    consistentDateAcrossDifferentChart?.ending_date,
                })
              }
            />
          </div>
          <div>
            <StatisticCard
              title={
                <div className={styles.dashboardStatisticsTitle}>
                  <FaRegUser />
                  <p>Total Open Issues</p>
                </div>
              }
              value={"--"}
              // suffixValue="K"
              percentage={" -- "}
              isPositive
            />
          </div>
        </div>

        {/* Billing Snapshots */}
        <div className={styles.dashboardFirst}>
          <BillingSnapshotCard
            title="Billing Snapshot"
            progressPercent={totalPercentage?.toFixed(2)}
            revenueGrowth={changeInPercentage?.toFixed(2)}
            footerText="Your M-o-M revenue grew by 12% "
            statistics={statisticsData}
            onMoreClick={() => console.log("More options clicked")}
          />

          {/* Previous Month Revenue */}
          <div className="previous-month-revenue">
            <ChartComponent
              title="Previous Month Revenue"
              subtitle={`\u20B9 ${totalPreviousMonthRevenue || 0}`}
              chartData={previousMonthRevenue || 0}
              xAxisDataKey="date"
              yAxisFormatter={(value) => {
                return `${value}`;
              }}
              areaDataKeys={["Revenue", "Sales"]}
              colors={colorsOne}
              percentageChange={" -- "}
              horizontal
              vertical={false}
              tickCount={8}
              interval={3 || revenueinterval}
              more
            />
          </div>
        </div>

        {/* Total Revenue and Meetings */}
        <div className={styles.dashboardSecond}>
          <div className="total-revenue">
            <ChartComponent
              title="Total Revenue"
              dispatch={dispatch}
              subtitle={`\u20B9${totalRevenueValue || 0}`}
              chartData={totalRevenue}
              xAxisDataKey="date"
              yAxisFormatter={(value) => `${value}`} // Format Y-axis in thousands
              areaDataKeys={["Revenue", "Sales"]}
              colors={colorsTwo}
              iconType="up"
              percentageChange={" -- "}
              height={650}
              horizontal={false}
              vertical={false}
              dateppicker
              startDate={
                totalRevenueDate?.starting_date ||
                consistentDateAcrossDifferentChart?.starting_date
              }
              endDate={
                totalRevenueDate?.ending_date ||
                consistentDateAcrossDifferentChart?.ending_date
              }
              setDate={setTotalRevenueDate}
              interval={1 || revenueinterval}
            />
          </div>

          {/* Total Meeting Hours */}
          <div className="total-meeting-hour">
            <div
              style={{
                width: "auto",
                borderBottomColor: "rgba(255,255,255,0.3)",
                borderBottomWidth: "1px",
                borderBottomStyle: "solid",
              }}
            >
              <LineContainer
                title={<div>Total Meetings</div>}
                value={<div>{totalMeetCount || 0}</div>}
                valueChange=" -- "
                valueChangeColor="rgba(20, 202, 116, 1)"
                chartType="line"
                data={totalMeetingData}
                dataKeyPrimary="meetings"
                colorPrimary="rgba(203, 60, 255, 1)"
                height={250}
                datepicker
                setDate={settotalMeetingHoursDate}
                interval={
                  window.innerWidth < 1000
                    ? window.innerWidth < 750
                      ? window.innerWidth < 600
                        ? window.innerWidth < 500
                          ? 50
                          : 40
                        : 30
                      : 20
                    : 50 || revenuehoursinterval
                }
                dispatch={dispatch}
                start_date={
                  totalMeetingHoursDate?.starting_date ||
                  consistentDateAcrossDifferentChart?.starting_date
                }
                end_date={
                  totalMeetingHoursDate?.ending_date ||
                  consistentDateAcrossDifferentChart?.ending_date
                }
              />
              <div className="graphFooter">
                <div className="graphFooterBody">
                  <div className="liveSection">
                    <Badge color="rgba(5, 193, 104, 1)" />
                    <i style={{ color: "rgba(5, 193, 104, 1)" }}>Live</i>
                  </div>
                  <div>
                    <i style={{ color: "rgba(126, 137, 172, 1)" }}>
                      -- Visitors
                    </i>
                  </div>
                </div>
                {/* <div className="graphFooterRight">
                  <i style={{ color: "rgba(203, 60, 255, 1)" }}>View report</i>
                </div> */}
              </div>
            </div>
            <div style={{ width: "auto" }}>
              <BarContainer
                title={<div>Total Meeting Hours</div>}
                value={<div>{totalMeetHours || 0}</div>}
                percentage=" -- "
                data={totalMeetingHoursData}
                dataKey="date"
                valueKey="meetings"
                color="rgba(0, 194, 255, 1)"
                borderColor="rgba(5, 193, 104, 0.2)"
                labelColor="rgba(20, 202, 116, 1)"
                interval={
                  window.innerWidth < 1000
                    ? window.innerWidth < 750
                      ? window.innerWidth < 600
                        ? window.innerWidth < 500
                          ? 50
                          : 40
                        : 30
                      : 20
                    : 50 || revenuehoursinterval
                }
              />
              {/* <div className="graphFooter">
                <div className="graphFooterBody" changeIn>
                  <p style={{ margin: "0px" }}>Last 12 months</p>
                </div>
                <div className="graphFooterRight">
                  <i style={{ color: "rgba(203, 60, 255, 1)" }}>View report</i>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// const mapStateToProps = (state) => {
//   const { loading, count, inactiveCount, activelicenceCount } =
//     state.table || {};
//   const {
//     dues,
//     message,
//     netRevenue,
//     revenue,
//     totalPercentage,
//     previousMonthRevenue,
//     changeInPercentage,
//     totalRevenue,
//     totalMeetingData,
//     totalMeetingHoursData,
//     totalMeetCount,
//     totalMeetHours,
//     totalRevenueValue,
//     consistentDateAcrossDifferentChart,
//   } = state?.dashboard || {};
//   const { userDetails } = state?.profile || {};

//   return {
//     loading,
//     count,
//     inactiveCount,
//     activelicenceCount,
//     dues,
//     message,
//     netRevenue,
//     revenue,
//     totalPercentage,
//     changeInPercentage,
//     previousMonthRevenue,
//     totalRevenue,
//     totalMeetingData,
//     totalMeetingHoursData,
//     totalMeetCount,
//     totalMeetHours,
//     userDetails,
//     totalRevenueValue,
//     consistentDateAcrossDifferentChart,
//   };
// };

// const mapDispatchToProps = {
//   previousMonthRevenueData,
//   totalRevenueData,
//   totalMeeting,
//   fetchTableData,
//   fetchUserData,
// };
