$primary-color: #0b1739;
$secondary-color: #5c59e8;
$hover-color: #1a2b6f;
$secondary-hover-color: #3e3cba;

.dashboard{
  display: flex;
  flex-direction: column;
  gap: 20px;
  &Header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    @media screen and (max-width: 500px) {
      flex-direction: column;
      gap: 10px;
    }
    &Left{
      display: flex;
      align-items: center;
      gap: 10px;
    }
    &Buttons{
      display: flex;
      gap: 20px;
      align-items: center;
      @media screen and (max-width: 500px) {
        width: 100%;
      }
    }
    &ButtonsExport{
      background-color: $primary-color;
      border-width: 0px;
      display: flex;
      align-items: center;
      color: white;
      &:hover{
        background-color: $hover-color;
        color: white;
      }
    }
    &ButtonsCreateReport{
      background-color: $secondary-color;
      border-width: 0px;
      display: flex;
      align-items: center;
      color: white;
      &:hover{
        background-color: $secondary-hover-color;
        color: white;
      }
    }
  }
  &Title{
    margin: 0;
    color: $primary-color;
    font-size: 2.5rem;
  }
  &Subtitle{
    color: $primary-color;
  }
  &DatePicker{
    display: flex;
    justify-content: space-between;
    align-items: center;
    &Date{
      background-color: $primary-color;
      border-width: 0px;
      padding: 7px 15px;
      border-radius: 10px;
      p{
        color: white;
      }
    }
    &Input{
      background-color: $primary-color;
      border-width: 0px;
      padding: 10px;
      border-radius: 10px;
      input{
        color: white;
      }
      svg{
        color: white !important;
      }
    }
  }
  &Statistics{
    display: flex;
    flex-direction: column;
    gap: 20px;
    &Section{
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
    }
    &Card{
      background-color: $primary-color;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
    }
    &Cards{
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      width: 100%;
      @media screen and (max-width: 1000px) {
        grid-template-columns: repeat(2, 1fr);
      }
      @media screen and (max-width: 650px) {
        grid-template-columns: repeat(1, 1fr);
      }
    }
    &Title{
      display: flex;
      align-items: center;
      gap: 10px;
      svg{
        color: white;
        width: 1.1rem;
        height: 1.1rem;
      }
      p{
        color: white;
        font-size: 16px;
        font-weight: 400;
        text-wrap: nowrap;
      }
    }
  }
  &First{
    display: grid;
    grid-template-columns: 1.5fr 2fr;
    gap: 20px;
    width: 100%;
    background-color: $primary-color;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
    @media screen and (max-width: 1000px) {
      grid-template-columns: repeat(1, 1fr);
    }
  }
  &Second{
    display: grid;
    grid-template-columns: 2fr 1.5fr;
    gap: 10px;
    width: 100%;
    background-color: $primary-color;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
    @media screen and (max-width: 1000px) {
      grid-template-columns: repeat(1, 1fr);
    }
  }
  &Filters{
    background-color: $primary-color;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
    :global(.ant-row){
      @media screen and (max-width: 650px) {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        :global(.ant-col){
          width: 100%;
        }
      }
    }
    &Row{
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 10px;
      align-items: center;
      @media screen and (max-width: 650px) {
        grid-template-columns: repeat(2, 1fr);
      }
      &Buttons{
        display: flex;
        gap: 10px;
      }
    }
    &Button{
      background-color: $secondary-color;
      border-width: 0px;
      display: flex;
      align-items: center;
      &:hover{
        background-color: $secondary-hover-color;
        color: white;
      }
      &:focus{
        background-color: $secondary-hover-color;
        color: white;
      }
    }
  }
  &Table{
    background-color: $primary-color;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
    &Container{
      overflow-x: auto;
    }
    &Table{
      min-width: 782px;
      :global(.ant-table){
        background-color: $primary-color;
        border-radius: 10px;
        box-shadow: 0px 0px 2px rgba(255, 255, 255, 0.6);
      }
    }
  }
}