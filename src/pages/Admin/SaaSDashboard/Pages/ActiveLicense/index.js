import React, { useEffect, useState } from "react";
import {
  Table,
  Input,
  Button,
  DatePicker,
  Tag,
  Space,
  Avatar,
  Statistic,
  Pagination,
  Progress,
} from "antd";
import moment from "moment";
import {
  UserOutlined,
  ArrowLeftOutlined,
  ExportOutlined,
  FilterOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { fetchActiveLicenseData } from "../../Store/slices/accountSlice";
import { DATE_FORMAT_DD_MMM_YY } from "../../Constants/DateConstant";
import styles from "../../Styles/defaultDashBoard.module.scss";
import adminRoutesMap from "../../../../../routeControl/adminRoutes";

const { Search } = Input;
const { RangePicker } = DatePicker;

export default function ActiveLicense() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const { activeLicenseData, loading } = useSelector(
    (state) => state.table || {}
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");

  // Get initial date range from URL parameters
  const getInitialDateRange = () => {
    const startDate = searchParams.get("start");
    const endDate = searchParams.get("end");
    if (startDate && endDate) {
      return {
        starting_date: startDate,
        ending_date: endDate,
      };
    }
    return null;
  };

  const [dateRange, setDateRange] = useState(getInitialDateRange());

  const handleBackToDashboard = () => {
    navigate(adminRoutesMap.SAAS_DASHBOARD.path);
  };

  const fetchData = () => {
    const params = {
      data_required: "all",
      currentPage: currentPage - 1,
      pageSize,
      starting_date: dateRange?.starting_date || "",
      ending_date: dateRange?.ending_date || "",
    };
    dispatch(fetchActiveLicenseData(params));
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, dateRange]);

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleDateRangeChange = (dates) => {
    const newDateRange = dates
      ? {
          starting_date: moment(dates[0]).format("YYYY-MM-DD"),
          ending_date: moment(dates[1]).format("YYYY-MM-DD"),
        }
      : null;
    setDateRange(newDateRange);
    setCurrentPage(1);
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const filteredData =
    activeLicenseData?.filter((item) =>
      searchText
        ? item.company_name?.toLowerCase().includes(searchText.toLowerCase()) ||
          item.license_key?.toLowerCase().includes(searchText.toLowerCase()) ||
          item.contact_person?.toLowerCase().includes(searchText.toLowerCase())
        : true
    ) || [];

  const getDaysUntilExpiry = (expiryDate) => {
    return moment(expiryDate).diff(moment(), "days");
  };

  const getExpiryStatus = (expiryDate) => {
    const daysLeft = getDaysUntilExpiry(expiryDate);
    if (daysLeft < 0) return { status: "expired", color: "#ff4d4f" };
    if (daysLeft <= 30) return { status: "expiring", color: "#faad14" };
    return { status: "active", color: "#52c41a" };
  };

  const columns = [
    {
      title: "Company",
      dataIndex: "company_name",
      key: "company_name",
      render: (text, record) => (
        <Space>
          <Avatar
            size={40}
            icon={<UserOutlined />}
            style={{ backgroundColor: "#52c41a" }}
          />
          <div>
            <div style={{ fontWeight: 600, color: "#fff" }}>{text}</div>
            <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
              {record.license_key || "N/A"}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "License Type",
      dataIndex: "license_type",
      key: "license_type",
      render: (type) => (
        <Tag
          color={
            type === "Premium"
              ? "gold"
              : type === "Enterprise"
              ? "purple"
              : type === "Professional"
              ? "blue"
              : "default"
          }
        >
          {type || "Basic"}
        </Tag>
      ),
    },
    {
      title: "Users",
      dataIndex: "max_users",
      key: "max_users",
      render: (maxUsers, record) => {
        const activeUsers = record.active_users || 0;
        const percentage = maxUsers ? (activeUsers / maxUsers) * 100 : 0;

        return (
          <div style={{ color: "#fff" }}>
            <div>
              {activeUsers} / {maxUsers || "N/A"}
            </div>
            {maxUsers && (
              <Progress
                percent={percentage}
                size="small"
                showInfo={false}
                strokeColor={percentage >= 90 ? "#ff4d4f" : "#52c41a"}
              />
            )}
          </div>
        );
      },
    },
    {
      title: "Created Date",
      dataIndex: "created_at",
      key: "created_at",
      render: (date) => (
        <div style={{ color: "#fff" }}>
          {date ? moment(date).format(DATE_FORMAT_DD_MMM_YY) : "N/A"}
        </div>
      ),
    },
    {
      title: "Expiry Date",
      dataIndex: "expiry_date",
      key: "expiry_date",
      render: (date) => {
        if (!date) return <span style={{ color: "#8c8c8c" }}>N/A</span>;

        const daysLeft = getDaysUntilExpiry(date);
        const { status, color } = getExpiryStatus(date);

        return (
          <div style={{ color }}>
            {status === "expired" ? (
              <ClockCircleOutlined style={{ marginRight: 8 }} />
            ) : (
              <CheckCircleOutlined style={{ marginRight: 8 }} />
            )}
            <div>{moment(date).format(DATE_FORMAT_DD_MMM_YY)}</div>
            <div style={{ fontSize: "12px" }}>
              {daysLeft > 0
                ? `${daysLeft} days left`
                : `Expired ${Math.abs(daysLeft)} days ago`}
            </div>
          </div>
        );
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status, record) => {
        if (record.expiry_date) {
          const { status: expiryStatus, color } = getExpiryStatus(
            record.expiry_date
          );
          return (
            <Tag
              color={
                color === "#52c41a"
                  ? "green"
                  : color === "#faad14"
                  ? "orange"
                  : "red"
              }
            >
              {expiryStatus.toUpperCase()}
            </Tag>
          );
        }
        return <Tag color="green">{status || "ACTIVE"}</Tag>;
      },
    },
    {
      title: "Action",
      key: "action",
      // eslint-disable-next-line no-unused-vars
      render: (_, record) => (
        <Space>
          <Button size="small" type="primary" ghost>
            Renew
          </Button>
          <Button size="small" type="default" ghost>
            Details
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.dashboard}>
      {/* Header */}
      <div className={styles.dashboardHeader}>
        <div className={styles.dashboardHeaderLeft}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBackToDashboard}
            className={styles.dashboardHeaderButton}
          />
          <div>
            <h1 className={styles.dashboardTitle}>Active Licenses</h1>
            <p className={styles.dashboardSubtitle}>
              Monitor and manage active software licenses
            </p>
          </div>
        </div>
        <Button
          icon={<ExportOutlined />}
          className={styles.dashboardHeaderButtonsExport}
        >
          Export Data
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className={styles.dashboardStatisticsCards}>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Total Active</span>}
            value={filteredData.length}
            valueStyle={{ color: "#52c41a" }}
            suffix={<CheckCircleOutlined />}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Expiring Soon</span>}
            value={
              filteredData.filter((license) => {
                if (!license.expiry_date) return false;
                const daysLeft = getDaysUntilExpiry(license.expiry_date);
                return daysLeft > 0 && daysLeft <= 30;
              }).length
            }
            valueStyle={{ color: "#faad14" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Enterprise</span>}
            value={
              filteredData.filter(
                (license) => license.license_type === "Enterprise"
              ).length
            }
            valueStyle={{ color: "#722ed1" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Total Users</span>}
            value={filteredData.reduce(
              (sum, license) => sum + (license.active_users || 0),
              0
            )}
            valueStyle={{ color: "#1890ff" }}
          />
        </div>
      </div>

      {/* Filters */}
      <div className={styles.dashboardFilters}>
        <div className={styles.dashboardFiltersRow}>
          <Search
            placeholder="Search by company, license key, or contact"
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: "100%" }}
          />
          <RangePicker
            onChange={handleDateRangeChange}
            style={{ width: "100%", height: "100%" }}
            placeholder={["Start Date", "End Date"]}
            value={
              dateRange
                ? [
                    moment(dateRange.starting_date),
                    moment(dateRange.ending_date),
                  ]
                : null
            }
          />
          <div className={styles.dashboardFiltersRowButtons}>
            <Button
              onClick={fetchData}
              style={{
                background: "rgba(92, 89, 232, 1)",
                border: "none",
                color: "#fff",
              }}
            >
              Refresh
            </Button>
            <Button
              icon={<FilterOutlined />}
              style={{
                background: "transparent",
                border: "1px solid rgba(255,255,255,0.3)",
                color: "#fff",
              }}
            >
              More Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className={styles.dashboardTable}>
        <div className={styles.dashboardTableContainer}>
          <Table
            columns={columns}
            dataSource={filteredData}
            loading={loading}
            rowKey="id"
            pagination={false}
            className={styles.dashboardTableTable}
          />
        </div>

        <div
          style={{
            marginTop: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span style={{ color: "#8c8c8c" }}>
            Showing {(currentPage - 1) * pageSize + 1} to{" "}
            {Math.min(currentPage * pageSize, filteredData.length)} of{" "}
            {filteredData.length} entries
          </span>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={filteredData.length}
            showSizeChanger
            showQuickJumper
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
            pageSizeOptions={["10", "20", "50", "100"]}
            style={{ color: "#fff" }}
          />
        </div>
      </div>
    </div>
  );
}
