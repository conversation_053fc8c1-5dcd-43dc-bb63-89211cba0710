import React, { useEffect, useState } from "react";
import {
  Table,
  Input,
  Button,
  DatePicker,
  Tag,
  Space,
  Avatar,
  Statistic,
  Pagination,
} from "antd";
import moment from "moment";
import {
  UserOutlined,
  ArrowLeftOutlined,
  ExportOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { fetchInactiveTableData } from "../../Store/slices/accountSlice";
import { DATE_FORMAT_DD_MMM_YY } from "../../Constants/DateConstant";
import styles from "../../Styles/defaultDashBoard.module.scss";
import adminRoutesMap from "../../../../../routeControl/adminRoutes";

const { Search } = Input;
const { RangePicker } = DatePicker;

export default function InactiveAccounts() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const { inactiveTableData, loading, inactiveCount } = useSelector(
    (state) => state.table || {}
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");

  // Get initial date range from URL parameters
  const getInitialDateRange = () => {
    const startDate = searchParams.get("start");
    const endDate = searchParams.get("end");
    if (startDate && endDate) {
      return {
        starting_date: startDate,
        ending_date: endDate,
      };
    }
    return null;
  };

  const [dateRange, setDateRange] = useState(getInitialDateRange());

  const handleBackToDashboard = () => {
    navigate(adminRoutesMap.SAAS_DASHBOARD.path);
  };

  const fetchData = () => {
    const params = {
      data_required: true,
      currentPage: currentPage - 1,
      pageSize,
      starting_date: dateRange?.starting_date || "",
      ending_date: dateRange?.ending_date || "",
    };
    dispatch(fetchInactiveTableData(params));
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, dateRange]);

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleDateRangeChange = (dates) => {
    const newDateRange = dates
      ? {
          starting_date: moment(dates[0]).format("YYYY-MM-DD"),
          ending_date: moment(dates[1]).format("YYYY-MM-DD"),
        }
      : null;
    setDateRange(newDateRange);
    setCurrentPage(1);
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const filteredData =
    inactiveTableData?.filter((item) =>
      searchText
        ? item.company_name?.toLowerCase().includes(searchText.toLowerCase()) ||
          item.contact_person
            ?.toLowerCase()
            .includes(searchText.toLowerCase()) ||
          item.email?.toLowerCase().includes(searchText.toLowerCase())
        : true
    ) || [];

  const columns = [
    {
      title: "Company",
      dataIndex: "company_name",
      key: "company_name",
      render: (text, record) => (
        <Space>
          <Avatar
            size={40}
            icon={<UserOutlined />}
            style={{ backgroundColor: "#ff4d4f", opacity: 0.7 }}
          />
          <div>
            <div style={{ fontWeight: 600, color: "#fff", opacity: 0.8 }}>
              {text}
            </div>
            <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
              {record.business_name || "N/A"}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "Contact Person",
      dataIndex: "contact_person",
      key: "contact_person",
      render: (text, record) => (
        <div>
          <div style={{ color: "#fff", opacity: 0.8 }}>{text}</div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            {record.email}
          </div>
        </div>
      ),
    },
    {
      title: "Last License Type",
      dataIndex: "license_type",
      key: "license_type",
      render: (type) => (
        <Tag color="default" style={{ opacity: 0.7 }}>
          {type || "Basic"}
        </Tag>
      ),
    },
    {
      title: "Deactivation Reason",
      dataIndex: "deactivation_reason",
      key: "deactivation_reason",
      render: (reason) => (
        <Tag
          color={
            reason === "Payment Failed"
              ? "red"
              : reason === "User Request"
              ? "orange"
              : reason === "Contract Ended"
              ? "purple"
              : "default"
          }
        >
          {reason || "Not specified"}
        </Tag>
      ),
    },
    {
      title: "Last Active",
      dataIndex: "last_active_date",
      key: "last_active_date",
      render: (date) => (
        <div style={{ color: "#fff", opacity: 0.8 }}>
          {date ? moment(date).format(DATE_FORMAT_DD_MMM_YY) : "N/A"}
        </div>
      ),
    },
    {
      title: "Deactivated Date",
      dataIndex: "expiry_Date",
      key: "deactivated_at",
      render: (date) => {
        console.log("date", date);
        return (
          <div style={{ color: "#ff4d4f" }}>
            {date ? moment(date).format(DATE_FORMAT_DD_MMM_YY) : "N/A"}
          </div>
        );
      },
    },
    {
      title: "Action",
      key: "action",
      // eslint-disable-next-line no-unused-vars
      render: (_, record) => (
        <Space>
          <Button size="small" type="primary" ghost>
            Reactivate
          </Button>
          <Button size="small" type="default" ghost>
            View History
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.dashboard}>
      {/* Header */}
      <div className={styles.dashboardHeader}>
        <div className={styles.dashboardHeaderLeft}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBackToDashboard}
            className={styles.dashboardHeaderButton}
          />
          <div>
            <h1 className={styles.dashboardTitle}>Inactive Accounts</h1>
            <p className={styles.dashboardSubtitle}>
              Review deactivated accounts and reactivation opportunities
            </p>
          </div>
        </div>
        <Button
          icon={<ExportOutlined />}
          className={styles.dashboardHeaderButtonsExport}
        >
          Export Data
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className={styles.dashboardStatisticsCards}>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Total Inactive</span>}
            value={inactiveCount || filteredData.length}
            valueStyle={{ color: "#ff4d4f" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>This Page</span>}
            value={filteredData.length}
            valueStyle={{ color: "#1890ff" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Payment Issues</span>}
            value={
              filteredData.filter(
                (acc) => acc.deactivation_reason === "Payment Failed"
              ).length
            }
            valueStyle={{ color: "#ff7875" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>User Requests</span>}
            value={
              filteredData.filter(
                (acc) => acc.deactivation_reason === "User Request"
              ).length
            }
            valueStyle={{ color: "#ffa940" }}
          />
        </div>
      </div>

      {/* Filters */}
      <div className={styles.dashboardFilters}>
        <div className={styles.dashboardFiltersRow}>
          <Search
            placeholder="Search by company, email, or contact person"
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: "100%" }}
          />
          <RangePicker
            onChange={handleDateRangeChange}
            style={{ width: "100%", height: "100%" }}
            placeholder={["Start Date", "End Date"]}
            value={
              dateRange
                ? [
                    moment(dateRange.starting_date),
                    moment(dateRange.ending_date),
                  ]
                : null
            }
          />
          <div className={styles.dashboardFiltersRowButtons}>
            <Button
              onClick={fetchData}
              style={{
                background: "rgba(92, 89, 232, 1)",
                border: "none",
                color: "#fff",
              }}
            >
              Refresh
            </Button>
            <Button
              icon={<FilterOutlined />}
              style={{
                background: "transparent",
                border: "1px solid rgba(255,255,255,0.3)",
                color: "#fff",
              }}
            >
              More Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className={styles.dashboardTable}>
        <div className={styles.dashboardTableContainer}>
          <Table
            columns={columns}
            dataSource={filteredData}
            loading={loading}
            rowKey="id"
            pagination={false}
            className={styles.dashboardTableTable}
          />
        </div>

        <div
          style={{
            marginTop: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span style={{ color: "#8c8c8c" }}>
            Showing {(currentPage - 1) * pageSize + 1} to{" "}
            {Math.min(currentPage * pageSize, inactiveCount || 0)} of{" "}
            {inactiveCount || 0} entries
          </span>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={inactiveCount || 0}
            showSizeChanger
            showQuickJumper
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
            pageSizeOptions={["10", "20", "50", "100"]}
            style={{ color: "#fff" }}
          />
        </div>
      </div>
    </div>
  );
}
