import React, { useEffect, useState } from "react";
import {
  Table,
  Input,
  Button,
  DatePicker,
  Tag,
  Space,
  Avatar,
  Statistic,
  Pagination,
} from "antd";
import moment from "moment";
import {
  UserOutlined,
  ArrowLeftOutlined,
  ExportOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { fetchActiveTableData } from "../../Store/slices/accountSlice";
import { DATE_FORMAT_DD_MMM_YY } from "../../Constants/DateConstant";
import styles from "../../Styles/defaultDashBoard.module.scss";
import adminRoutesMap from "../../../../../routeControl/adminRoutes";

const { Search } = Input;
const { RangePicker } = DatePicker;

export default function ActiveAccounts() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const { tableData, loading, count } = useSelector(
    (state) => state.table || {}
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");

  // Get initial date range from URL parameters
  const getInitialDateRange = () => {
    const startDate = searchParams.get("start");
    const endDate = searchParams.get("end");
    if (startDate && endDate) {
      return {
        starting_date: startDate,
        ending_date: endDate,
      };
    }
    return null;
  };

  const [dateRange, setDateRange] = useState(getInitialDateRange());

  const fetchData = () => {
    const params = {
      data_required: true,
      currentPage: currentPage - 1,
      pageSize,
      starting_date: dateRange?.starting_date || "",
      ending_date: dateRange?.ending_date || "",
    };
    dispatch(fetchActiveTableData(params));
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, dateRange]);

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleDateRangeChange = (dates) => {
    const newDateRange = dates
      ? {
          starting_date: moment(dates[0]).format("YYYY-MM-DD"),
          ending_date: moment(dates[1]).format("YYYY-MM-DD"),
        }
      : null;
    setDateRange(newDateRange);
    setCurrentPage(1);
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const handleBackToDashboard = () => {
    navigate(adminRoutesMap.SAAS_DASHBOARD.path);
  };

  const filteredData =
    tableData?.filter((item) =>
      searchText
        ? item.company_name?.toLowerCase().includes(searchText.toLowerCase()) ||
          item.contact_person
            ?.toLowerCase()
            .includes(searchText.toLowerCase()) ||
          item.email?.toLowerCase().includes(searchText.toLowerCase())
        : true
    ) || [];

  const columns = [
    {
      title: "Company",
      dataIndex: "company_name",
      key: "company_name",
      render: (text, record) => (
        <Space>
          <Avatar
            size={40}
            icon={<UserOutlined />}
            style={{ backgroundColor: "#1890ff" }}
          />
          <div>
            <div style={{ fontWeight: 600, color: "#fff" }}>{text}</div>
            <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
              {record.business_type || "N/A"}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "Contact Person",
      dataIndex: "contact_person",
      key: "contact_person",
      render: (text, record) => (
        <div>
          <div style={{ color: "#fff" }}>{text}</div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            {record.email}
          </div>
        </div>
      ),
    },
    {
      title: "License Type",
      dataIndex: "license_type",
      key: "license_type",
      render: (type) => (
        <Tag
          color={
            type === "Premium"
              ? "gold"
              : type === "Enterprise"
              ? "purple"
              : "blue"
          }
        >
          {type || "Basic"}
        </Tag>
      ),
    },
    {
      title: "Total Users",
      dataIndex: "total_users",
      key: "total_users",
      render: (counts) => (
        <div style={{ color: "#fff", textAlign: "center" }}>{counts || 0}</div>
      ),
    },
    {
      title: "Created Date",
      dataIndex: "created_at",
      key: "created_at",
      render: (date) => (
        <div style={{ color: "#fff" }}>
          {moment(date).format(DATE_FORMAT_DD_MMM_YY)}
        </div>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status) => <Tag color="green">{status || "Active"}</Tag>,
    },
    {
      title: "Action",
      key: "action",
      // eslint-disable-next-line no-unused-vars
      render: (_, record) => (
        <Space>
          <Button size="small" type="primary" ghost>
            View Details
          </Button>
          <Button size="small" type="default" ghost>
            Edit
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.dashboard}>
      {/* Header */}
      <div className={styles.dashboardHeader}>
        <div className={styles.dashboardHeaderLeft}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBackToDashboard}
            className={styles.dashboardHeaderButton}
          />
          <div>
            <h1 className={styles.dashboardTitle}>Active Accounts</h1>
            <p className={styles.dashboardSubtitle}>
              Manage and monitor your active customer accounts
            </p>
          </div>
        </div>
        <Button
          icon={<ExportOutlined />}
          className={styles.dashboardHeaderButtonsExport}
        >
          Export Data
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className={styles.dashboardStatisticsCards}>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Total Active</span>}
            value={count || filteredData.length}
            valueStyle={{ color: "#52c41a" }}
            suffix={<UserOutlined />}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>This Page</span>}
            value={filteredData.length}
            valueStyle={{ color: "#1890ff" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Premium Users</span>}
            value={
              filteredData.filter((acc) => acc.license_type === "Premium")
                .length
            }
            valueStyle={{ color: "#faad14" }}
          />
        </div>
        <div className={styles.dashboardStatisticsCard}>
          <Statistic
            title={<span style={{ color: "#8c8c8c" }}>Total Users</span>}
            value={filteredData.reduce(
              (sum, acc) => sum + (acc.total_users || 0),
              0
            )}
            valueStyle={{ color: "#722ed1" }}
          />
        </div>
      </div>

      {/* Filters */}
      <div className={styles.dashboardFilters}>
        <div className={styles.dashboardFiltersRow}>
          <Search
            placeholder="Search by company, email, or contact person"
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: "100%" }}
          />
          <RangePicker
            onChange={handleDateRangeChange}
            style={{ width: "100%", height: "100%" }}
            placeholder={["Start Date", "End Date"]}
            value={
              dateRange
                ? [
                    moment(dateRange.starting_date),
                    moment(dateRange.ending_date),
                  ]
                : null
            }
          />
          <div className={styles.dashboardFiltersRowButtons}>
            <div className={styles.dashboardFiltersCol}>
              <Button
                onClick={fetchData}
                className={styles.dashboardFiltersButton}
              >
                Refresh
              </Button>
            </div>
            <div className={styles.dashboardFiltersCol}>
              <Button
                icon={<FilterOutlined />}
                style={{
                  background: "transparent",
                  border: "1px solid rgba(255,255,255,0.3)",
                  color: "#fff",
                }}
              >
                More Filters
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className={styles.dashboardTable}>
        <div className={styles.dashboardTableContainer}>
          <Table
            columns={columns}
            dataSource={filteredData}
            loading={loading}
            rowKey="id"
            pagination={false}
            className={styles.dashboardTableTable}
          />
        </div>

        <div
          style={{
            marginTop: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span style={{ color: "#8c8c8c" }}>
            Showing {(currentPage - 1) * pageSize + 1} to{" "}
            {Math.min(currentPage * pageSize, count || 0)} of {count || 0}{" "}
            entries
          </span>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={count || 0}
            showSizeChanger
            showQuickJumper
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
            pageSizeOptions={["10", "20", "50", "100"]}
            style={{ color: "#fff" }}
          />
        </div>
      </div>
    </div>
  );
}
