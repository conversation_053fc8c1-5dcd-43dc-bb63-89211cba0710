import { AUTH_TOKEN } from "../Constants/AuthConstant";

const baseUrl = process.env.REACT_APP_STAG_BACKEND_BASE_URL;

export const AccountService = {
  fetchActiveTableData: (
    payload
  ) => {
    if (payload?.starting_date && payload?.ending_date) {
      return fetch(
        `${baseUrl}/saas/masterAdmin/active/accounts?data_required=${payload?.data_required}&start=${payload?.currentPage}&max=${payload?.pageSize}&start_date=${payload?.starting_date}&end_date=${payload?.ending_date}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    } else {
      return fetch(
        `${baseUrl}/saas/masterAdmin/active/accounts?data_required=${payload?.data_required}&start=${payload?.currentPage}&max=${payload?.pageSize}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    }
  },

  fetchActiveLicenseData: (
    payload
  ) => {
    return fetch(
      payload.starting_date && payload.ending_date
        ? `${baseUrl}/saas/masterAdmin/active/licenses?data_required=${payload.data_required}&start=${payload.currentPage}&max=${payload.pageSize}&start_date=${payload.starting_date}&end_date=${payload.ending_date}`
        : `${baseUrl}/saas/masterAdmin/active/licenses?data_required=${payload.data_required}&start=${payload.currentPage}&max=${payload.pageSize}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${AUTH_TOKEN}`,
        },
      }
    );
  },

  fetchInActiveTableData: (
    payload
  ) => {
    if (payload?.starting_date && payload?.ending_date) {
      return fetch(
        `${baseUrl}/saas/masterAdmin/inactive/accounts?data_required=${payload?.data_required}&start=${payload?.currentPage}&max=${payload?.pageSize}&start_date=${payload?.starting_date}&end_date=${payload?.ending_date}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    } else {
      return fetch(
        `${baseUrl}/saas/masterAdmin/inactive/accounts?data_required=${payload?.data_required}&start=${payload?.currentPage}&max=${payload?.pageSize}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    }
  },
  fetchTableData: (
    payload
  ) => {
    if (payload?.starting_date && payload?.ending_date) {
      return fetch(
        `${baseUrl}/saas/masterAdmin/dash/count/data?start_date=${payload?.starting_date}&end_date=${payload?.ending_date}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    } else {
      console.log("AUTH_TOKEN idhar hai", AUTH_TOKEN);
      return fetch(
        `${baseUrl}/saas/masterAdmin/dash/count/data`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    }
  },
};
