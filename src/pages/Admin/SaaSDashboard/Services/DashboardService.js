import { AUTH_TOKEN } from "../Constants/AuthConstant";

const baseUrl = process.env.REACT_APP_STAG_BACKEND_BASE_URL;

export const DashboardService = {
  previousMonthRevenueData: (previousMonthRevenueDate) => {
    return fetch(
      previousMonthRevenueDate?.starting_date &&
        previousMonthRevenueDate.ending_date
        ? `${baseUrl}/saas/masterAdmin/dash/prev/rev/data?start_date=${previousMonthRevenueDate.starting_date}&end_date=${previousMonthRevenueDate.ending_date}`
        : `${baseUrl}/saas/masterAdmin/dash/prev/rev/data`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${AUTH_TOKEN}`,
        },
      }
    );
  },

  totalRevenueData: (totalRevenueDate) => {
    if (totalRevenueDate.starting_date && totalRevenueDate.ending_date) {
      return fetch(
        `${baseUrl}/saas/masterAdmin/dash/total/rev/data?start_date=${totalRevenueDate.starting_date}&end_date=${totalRevenueDate.ending_date}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    } else {
      return fetch(
        `${baseUrl}/saas/masterAdmin/dash/total/rev/data`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    }
  },

  totalMeeting: (totalMeetingHoursDate) => {
    if (
      totalMeetingHoursDate.starting_date &&
      totalMeetingHoursDate.ending_date
    ) {
      return fetch(
        `${baseUrl}/saas/masterAdmin/dash/meeting/data?start_date=${totalMeetingHoursDate.starting_date}&end_date=${totalMeetingHoursDate.ending_date}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    } else {
      return fetch(
        `${baseUrl}/saas/masterAdmin/dash/meeting/data`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    }
  },

  totalMeetingHours: (totalMeetingHoursDate) => {
    if (
      totalMeetingHoursDate.starting_date &&
      totalMeetingHoursDate.ending_date
    ) {
      return fetch(
        `${baseUrl}/saas/masterAdmin/total/meetings/hours?start_date=${totalMeetingHoursDate.starting_date}&end_date=${totalMeetingHoursDate.ending_date}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    } else {
      return fetch(
        `${baseUrl}/saas/masterAdmin/total/meetings/hours`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`,
          },
        }
      );
    }
  },
};
