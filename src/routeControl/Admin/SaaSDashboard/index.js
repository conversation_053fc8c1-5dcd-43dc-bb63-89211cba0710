import { RiAdminLine } from "react-icons/ri";
import styles from "./SaaSDashbaord.module.scss";

const accessRoute = {
    SAAS: {
      path: "/admin/saas",
      icon: (
        <span className={`nk-menu-icon ${styles.saasDashboardIcon}`}>
          <RiAdminLine />
        </span>
      ),
    },
    SAAS_DASHBOARD: {
      path: "/admin/saas/dashboard",
    },
    SAAS_PROFILE: {
      path: "/admin/saas/profile",
    },
    // Add routes for internal dashboard pages
    SAAS_ACTIVE_ACCOUNTS: {
      path: "/admin/saas/dashboard/active-accounts",
    },
    SAAS_INACTIVE_ACCOUNTS: {
      path: "/admin/saas/dashboard/inactive-accounts", 
    },
    SAAS_ACTIVE_LICENSE: {
      path: "/admin/saas/dashboard/active-license",
    },
  };
  
  export default accessRoute;
  