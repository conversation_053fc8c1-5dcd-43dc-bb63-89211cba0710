import { SocialMediaLinks } from "../../../../apiEndPoints/Admin";
import { logger } from "../../../../utils";
import APIrequest from "../../../axios";

export const AdminSocialMediaLinksServices = {
  /**
   * Function to get social media links
   * @returns {Promise} API response
   */
  getSocialMediaLinks: async ({ queryParams = {} } = {}) => {
    try {
      const payload = {
        ...SocialMediaLinks.getSocialMediaLinks,
        queryParams,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },

  /**
   * Function to update social media links
   * @param {Object} bodyData - The social media links data to update (should include id)
   * @returns {Promise} API response
   */
  updateSocialMediaLinks: async (bodyData) => {
    try {
      const { id, ...updateData } = bodyData; // Extract id from bodyData
      const payload = {
        ...SocialMediaLinks.updateSocialMediaLinks,
        url: `/v1.0/admin/social-link/${id}`, // Override URL to include the id
        bodyData: updateData, // Send the rest of the data in body
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
};