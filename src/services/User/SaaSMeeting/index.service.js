import { SaaSMeeting } from "../../../apiEndPoints";
import { logger } from "../../../utils";
import {AppAPIRequest} from "../../axios";

export const SaaSMeetingService = {
    checkMeetingExistService: async (meetingId) => {
        try {
            const payload = {
                ...SaaSMeeting.checkMeetingExist(meetingId),
            }
            const res = await AppAPIRequest(payload);
            return res;
        } catch (error) {
            logger(error);
            throw error;
        }
    },
    getMeetingConfig: async (meetingId) => {
        try {
            const payload = {
                ...SaaSMeeting.getMeetingConfig(meetingId),
            }
            const res = await AppAPIRequest(payload);
            return res;
        } catch (error) {
            logger(error);
            throw error;
        }
    },
    getMeetingFeatures: async (meetingId) => {
        try {
            const payload = {
                ...SaaSMeeting.getMeetingFeatures(meetingId),
            }
            const res = await AppAPIRequest(payload);
            return res;
        } catch (error) {
            logger(error);
            throw error;
        }
    },
    verifyHostEmail: async (bodyData) => {
        try {
            const payload = {
                ...SaaSMeeting.verifyHostEmail(),
                bodyData,
            }
            const res = await AppAPIRequest(payload);
            return res;
        } catch (error) {
            logger(error);
            throw error;
        }
    },
    getHostToken: async (meetingId) => {
        try {
            const payload = {
                ...SaaSMeeting.getHostToken(meetingId),
            }
            const res = await AppAPIRequest(payload);
            return res;
        } catch (error) {
            logger(error);
            throw error;
        }
    },
    checkHostJoinedMeeting: async (meetingId) => {
        try {
            const payload = {
                ...SaaSMeeting.checkHostJoinedMeeting(meetingId),
            }
            const res = await AppAPIRequest(payload);
            return res;
        } catch (error) {
            logger(error);
            throw error;
        }
    }
}