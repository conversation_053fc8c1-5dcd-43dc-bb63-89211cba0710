import { Outlet, Navigate } from "react-router-dom";
import {
  SaaSDashboard,
} from "../../../pages/Admin";
import adminRoutesMap from "../../../routeControl/adminRoutes";

// Import the internal page components
import { ActiveAccounts, InactiveAccounts, ActiveLicense } from "../../../pages/Admin/SaaSDashboard/Pages";

export default function route(t) {
  return [
    {
      path: adminRoutesMap.SAAS.path,
      private: true,
      name: t("text.saasDashboard.pageTitle"),
      key: adminRoutesMap.SAAS.path,
      belongsToSidebar: true,
      icon: adminRoutesMap.SAAS.icon,
      element: <Outlet />,
      children: [
        // Add index route to redirect /admin to /admin/saas/dashboard
        {
          index: true,
          element: <Navigate to={adminRoutesMap.SAAS_DASHBOARD.path} replace />,
        },
        {
          path: adminRoutesMap.SAAS_DASHBOARD.path,
          private: true,
          name: t("text.saasDashboard.landingPageTitle"),
          key: adminRoutesMap.SAAS_DASHBOARD.path,
          belongsToSidebar: true,
          element: <SaaSDashboard />,
        },
        {
          path: adminRoutesMap.SAAS_ACTIVE_ACCOUNTS.path,
          private: true,
          name: "Active Accounts",
          key: adminRoutesMap.SAAS_ACTIVE_ACCOUNTS.path,
          belongsToSidebar: false,
          element: <ActiveAccounts />,
        },
        {
          path: adminRoutesMap.SAAS_INACTIVE_ACCOUNTS.path,
          private: true,
          name: "Inactive Accounts", 
          key: adminRoutesMap.SAAS_INACTIVE_ACCOUNTS.path,
          belongsToSidebar: false,
          element: <InactiveAccounts />,
        },
        {
          path: adminRoutesMap.SAAS_ACTIVE_LICENSE.path,
          private: true,
          name: "Active License",
          key: adminRoutesMap.SAAS_ACTIVE_LICENSE.path,
          belongsToSidebar: false,
          element: <ActiveLicense />,
        },
      ],
    },
  ];
}
