const Translate = {
  languageListing: {
    url: "/languages",
    method: "GET",
  },
  translateText: {
    url: "/v1.0/translate/text",
    method: "POST",
  },
  translateHtml: {
    url: "/translate/html",
    method: "POST",
  },
  translateDocument: {
    url: "/translate/document",
    method: "POST",
  },
  getEncodedDocument: {
    url: "/translate/getDocument",
    method: "GET",
  },
  translateSpeechDocument: {
    url: "/translate/speech",
    method: "POST",
  },
  translateSpeechLanguageList: {
    url: "/speechToTextLanguages",
    method: "GET",
  },
  translateSpeechToText: {
    url: "/translate/speechToText",
    method: "POST",
  },
  translateCount: {
    url: "/v1.0/translation-data-count",
    method: "POST",
  },
  createJob: {
    url: "/translate/createJob",
    method: "POST",
  },
  getLanguageList: {
    url: "/translate/job/languages",
    method: "GET",
  },
  getJobsList: {
    url: "/translate/job/user",
    method: "GET",
  },
  getJobStatus: {
    url: "/translate/jobStatus",
    method: "GET",
  },
  documentTranslationLanguageList: {
    url: "/document/supported-languages",
    method: "GET",
  },
  singleDocumentTranslation: {
    url: "/document/translate",
    method: "POST",
  },
  multiDocumentTranslation: {
    url: "/document/translate-batch",
    method: "POST",
  },
  getTranslatedDocument: {
    url: "/document/batch-status",
    method: "GET",
  },
  history: {
    url: "/document",
    method: "GET",
  },
  deleteDocument: {
    url: "document/delete",
    method: "POST",
  },
  searchDocument: {
    url: "document/search",
    method: "POST",
  },
};
export default Translate;
