const SaaSMeeting = {
    checkMeetingExist: (meetingId) => ({
        url: `/meeting/exist?uid=${meetingId}`,
        method: "GET",
    }),
    getMeetingConfig: (meetingId) => ({
        url: `/saas/meeting/config?meeting_uid=${meetingId}`,
        method: "GET",
    }),
    getMeetingFeatures: (meetingId) => ({
        url: `/saas/meeting/features?meeting_uid=${meetingId}`,
        method: "GET",
    }),
    verifyHostEmail: () => ({
        url: `/meeting/verifyHost`,
        method: "POST",
    }),
    getHostToken: (meetingId) => ({
        url: `/saas/host/token?meeting_uid=${meetingId}`,
        method: "GET",
    }),
    checkHostJoinedMeeting: (meetingId) => ({
        url: `/saas/meeting/check/hostJoined?meeting_id=${meetingId}`,
        method: "GET",
    }),
}

export default SaaSMeeting;