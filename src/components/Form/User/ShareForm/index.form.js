import { Form, Formik } from "formik";
import { t } from "i18next";

import React from "react";
import "./index.scss";
import { <PERSON> } from "react-router-dom";
import moment from "moment";
import {
  EmailShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TelegramShareButton,
  WhatsappShareButton,
  EmailIcon,
  FacebookIcon,
  LinkedinIcon,
  TelegramIcon,
  WhatsappIcon,
} from "react-share";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { isMobileBrowser } from "@livekit/components-core";
import userRoutesMap from "../../../../routeControl/userRoutes";
import {
  baseUrlGenerator, dateFormatter,
  encoder,
  // logger,
  modalNotification,
} from "../../../../utils";
import { Input as TextInput } from "../../../Antd";
// import {Button} from "react-bootstrap";
import {CommonButton, RippleEffect} from "../../../UiElement";
// import { CommonServices } from "../../../../services";
// import { Col, Row } from "react-bootstrap";

export default function ShareForm({ onSubmit, shareId, shareMeetingInfo, sipData }) {
  // const [copyText, setCopyText] = useState('');

  // Use correct keys from API response
  const numbers = sipData?.sip_numbers || [];
  const sipDialingIp = sipData?.sip_dailing_ip || '';
  const sipPin = sipData?.sip_pin || '';

  const initialValues = {
    share: baseUrlGenerator(
      `${userRoutesMap.DAAKIA_VC_INVITEE.path}/${encoder(shareId)}`
    ),
  };

  const shareInfo =`Event Name: ${shareMeetingInfo.event_name}
  Date: ${shareMeetingInfo?.start_date ? moment(shareMeetingInfo.start_date).local().format('DD MMM YYYY'): ''}
  Time: ${shareMeetingInfo?.start_date ? dateFormatter(shareMeetingInfo.start_date, 'HH:mm') : ''} - ${shareMeetingInfo?.end_date ? dateFormatter(shareMeetingInfo.end_date, 'HH:mm') : ''} IST`;

  const copyInvitation =`Event Name: ${shareMeetingInfo.event_name}
Date: ${shareMeetingInfo?.start_date ? moment(shareMeetingInfo.start_date).local().format('DD MMM YYYY'): ''}
Time: ${shareMeetingInfo?.start_date ? dateFormatter(shareMeetingInfo.start_date, 'HH:mm') : ''} - ${shareMeetingInfo?.end_date ? dateFormatter(shareMeetingInfo.end_date, 'HH:mm') : ''} IST

${initialValues?.share}

Join by phone
${numbers.map((number) => (
  `${number?.formatted_phone.split(" (")[0]} (${number?.formatted_phone.split(" (")[1]}`
)).join('\n')}

Join by video system
Dial ${sipDialingIp}

PIN: ${sipPin} (Enter this PIN after dialling in)`;
  const [moreWays, setMoreWays] = React.useState(false);

  return (
    <div>
      <Formik
        initialValues={{ ...initialValues }}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {(props) => {
          return (
            <Form>
              <div className="modalHeader">
                <h3> {t("text.videoConferencing.invitePeople")}</h3>
              </div>
              <div className="shareModal_form">
                <div className="form-group copyClip w-100">
                  <label className="form-label font-bd">
                    {t("text.videoConferencing.shareOthers")}
                  </label>
                  <div className="form-control-wrap d-flex">
                    <TextInput
                      className="form-control"
                      name="share"
                      type="text"
                      setFieldValue={props.handleChange}
                      // value="http://localhost:3000/video-conferencing"
                      readOnly
                    />
                    <Link
                      className="copyClip_icon d-flex align-items-center justify-content-center"
                      to="#"
                      title={"Copy link"}
                      onClick={(e) => {
                        e.preventDefault();
                        window.navigator.clipboard.writeText(
                          props?.values?.share
                        );
                        modalNotification({
                          type: "success",
                          message: t("text.videoConferencing.linkCopy"),
                        });
                      }}
                    >
                      <em className="icon-copy" />
                    </Link>
                  </div>
                </div>
                <div className="form-group">
                  <label className="form-label font-bd">
                    {t("text.videoConferencing.shareInvitation")}
                  </label>
                  <div className="react-share">
                    <EmailShareButton
                                      url={props?.values?.share}
                                      body={shareInfo}
                                      subject={'You are invited to a Daakia Meeting'}
                                      separator={'\n'}>
                      <EmailIcon size="35" round />{" "}
                    </EmailShareButton>
                    <FacebookShareButton url={props?.values?.share}
                                        title={shareInfo}
                                        separator={'\n'}>
                      <FacebookIcon size="35" round />
                    </FacebookShareButton>
                    <LinkedinShareButton url={props?.values?.share}
                                        title={shareInfo}
                                        separator={'\n'}>
                      <LinkedinIcon size="35" round />
                    </LinkedinShareButton>
                    <TelegramShareButton url={props?.values?.share}
                                        title={shareInfo}
                                        separator={'\n'}>
                      <TelegramIcon size="35" round />
                    </TelegramShareButton>
                    <WhatsappShareButton url={props?.values?.share}
                                        title={shareInfo}
                                        separator={'\n'}>
                      <WhatsappIcon size="35" className={""} round />
                    </WhatsappShareButton>
                    <RippleEffect>
                      <CommonButton
                          variant="primary"
                          title={"Copy meeting invitation"}
                          extraClassName={"btn-sm fs-6 ms-2"}
                          onClick={(e) => {
                        e.preventDefault();
                        window.navigator.clipboard.writeText(
                            copyInvitation
                        );
                        modalNotification({
                          type: "success",
                          message: t("text.videoConferencing.copyInvitationNotification"),
                        });
                      }}>
                        {t("text.videoConferencing.copyInvitation")}
                      </CommonButton>
                    </RippleEffect>
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
      <div onClick={() => {
          setMoreWays(!moreWays);
        }} 
        style={{cursor: 'pointer', width: "max-content", fontSize: "18px", fontWeight: "bold", margin: "0.7rem 0"}
      }>More ways to join {moreWays ? <UpOutlined /> : <DownOutlined />}</div>
      {moreWays && (
        <div className="invite-more">
          {/* <p>Join from the meeting link</p>
          <Link to={baseUrlGenerator(`${userRoutesMap.DAAKIA_VC_INVITEE.path}/${encoder(shareId)}`)}>
            {baseUrlGenerator(`${userRoutesMap.DAAKIA_VC_INVITEE.path}/${encoder(shareId)}`)}
          </Link> */}
          <p className="invite-more-by-phone">Join by phone</p>
          <ul>
            {isMobileBrowser() ? (
              <>
                {numbers.map((number, index) => (
                  <li key={index}>
                    <a href={`tel:${number?.formatted_phone.split(" (")[0]}`}>
                      {number?.formatted_phone.split(" (")[0]}
                    </a>&nbsp;
                    <span>({number?.formatted_phone.split(" (")[1]}</span>
                  </li>
                ))}
              </>
            ) : (
              <>
                {numbers.map((number, index) => (
                  <li key={index} style={{color: "#277BF7"}}>
                    {number?.formatted_phone.split(" (")[0]}{" "}
                    <span style={{color: "#242424"}}>({number?.formatted_phone.split(" (")[1]}</span>
                  </li>
                ))}
              </>
            )}
          </ul>
          <div className="invite-more-by-ip">
            <p>Join by video system</p>
            <p className="invite-more-by-ip-code">
              <span>Dial </span>&nbsp;{sipDialingIp}
            </p>
          </div>
          <div className="invite-more-pin">
            PIN : <span>{sipPin} (Enter this PIN after dialing in)</span>
          </div>
        </div>
      )}
    </div>
  );
}